// 通用2D变换工具
export interface TransformParams {
  scale: number
  offsetX: number
  offsetY: number
  rotation?: number // 弧度
  anchorX?: number // 锚点（图片坐标系下，通常为 width/2）
  anchorY?: number
}

// 图片坐标 -> 画布坐标（支持缩放、平移、旋转、锚点）
export function imageToStage(
  pt: { x: number; y: number },
  params: TransformParams
): { x: number; y: number } {
  let { x, y } = pt
  // 1. 以锚点为中心旋转
  if (params.rotation && params.anchorX !== undefined && params.anchorY !== undefined) {
    const dx = x - params.anchorX
    const dy = y - params.anchorY
    const cos = Math.cos(params.rotation)
    const sin = Math.sin(params.rotation)
    x = params.anchorX + dx * cos - dy * sin
    y = params.anchorY + dx * sin + dy * cos
  }
  // 2. 缩放
  x = x * params.scale
  y = y * params.scale
  // 3. 平移
  x += params.offsetX
  y += params.offsetY
  return { x, y }
}

// 画布坐标 -> 图片坐标（逆变换）
export function stageToImage(
  pt: { x: number; y: number },
  params: TransformParams
): { x: number; y: number } {
  let x = pt.x - params.offsetX
  let y = pt.y - params.offsetY
  x = x / params.scale
  y = y / params.scale
  if (params.rotation && params.anchorX !== undefined && params.anchorY !== undefined) {
    const dx = x - params.anchorX
    const dy = y - params.anchorY
    const cos = Math.cos(-params.rotation)
    const sin = Math.sin(-params.rotation)
    x = params.anchorX + dx * cos - dy * sin
    y = params.anchorY + dx * sin + dy * cos
  }
  return { x, y }
}

/**
 * 计算点集的包围盒（最小矩形）
 * @param points 点集
 * @returns 包围盒 {minX, maxX, minY, maxY}
 */
export function getLipBoundingBox(points: { x: number; y: number }[]) {
  const xs = points.map((p) => p.x)
  const ys = points.map((p) => p.y)
  return {
    minX: Math.min(...xs),
    maxX: Math.max(...xs),
    minY: Math.min(...ys),
    maxY: Math.max(...ys)
  }
}

/**
 * 应用旋转变换到点集
 * @param points 原始点集
 * @param rotation 旋转角度（弧度）
 * @param centerX 旋转中心X坐标
 * @param centerY 旋转中心Y坐标
 * @returns 旋转后的点集
 */
export function applyRotationToPoints(
  points: { x: number; y: number }[],
  rotation: number,
  centerX: number,
  centerY: number
): { x: number; y: number }[] {
  if (!rotation || Math.abs(rotation) < 0.01) return points

  return points.map((p) => {
    // 相对于旋转中心的偏移
    const dx = p.x - centerX
    const dy = p.y - centerY

    // 应用旋转
    const cos = Math.cos(rotation)
    const sin = Math.sin(rotation)
    const rotatedX = centerX + dx * cos - dy * sin
    const rotatedY = centerY + dx * sin + dy * cos

    return { x: rotatedX, y: rotatedY }
  })
}

/**
 * 计算让唇线/牙齿区域适配画布的缩放与偏移参数
 * @param lipPoints 唇线点集
 * @param stageW 画布宽度
 * @param stageH 画布高度
 * @param options 可选参数
 * @returns 缩放和偏移参数
 */
export interface LipFocusOptions {
  targetRatio?: number // 唇线区域占画布的比例，默认0.4
  rotation?: number // 旋转角度（弧度）
  imgWidth?: number // 图片宽度，用于计算旋转中心
  imgHeight?: number // 图片高度，用于计算旋转中心
  returnCenter?: boolean // 是否返回中心点坐标而不是偏移量
}

export interface LipFocusResult {
  scale: number
  offsetX: number
  offsetY: number
  lipCenterX?: number
  lipCenterY?: number
  center?: { x: number; y: number } // 当 returnCenter 为 true 时返回
}

export function calcLipFocusScaleAndOffset(
  lipPoints: { x: number; y: number }[],
  stageW: number,
  stageH: number,
  options: LipFocusOptions = {}
): LipFocusResult {
  const {
    targetRatio = 0.4,
    rotation = 0,
    imgWidth = 0,
    imgHeight = 0,
    returnCenter = false
  } = options

  // 如果点集为空或只有一个点，返回默认值
  if (!lipPoints || lipPoints.length < 2) {
    if (returnCenter) {
      return {
        scale: 1,
        offsetX: 0,
        offsetY: 0,
        center: { x: stageW / 2, y: stageH / 2 }
      }
    }
    return { scale: 1, offsetX: 0, offsetY: 0 }
  }

  // 考虑旋转因素，先将唇线点集应用旋转变换
  let rotatedPoints = lipPoints
  if (rotation && Math.abs(rotation) > 0.01 && imgWidth && imgHeight) {
    // 图片中心点（旋转中心）
    const centerX = imgWidth / 2
    const centerY = imgHeight / 2

    // 应用旋转变换
    rotatedPoints = applyRotationToPoints(lipPoints, rotation, centerX, centerY)
  }

  // 计算旋转后唇线的包围盒
  const box = getLipBoundingBox(rotatedPoints)
  const lipW = box.maxX - box.minX
  const lipH = box.maxY - box.minY

  // 计算让唇线区域适配画布的缩放比例
  const scaleX = (stageW * targetRatio) / lipW
  const scaleY = (stageH * targetRatio) / lipH
  // 取较小的缩放因子，保证唇线整体都能显示在画布内
  const scale = Math.min(scaleX, scaleY)

  // 旋转后唇线中心点
  const lipCenterX = (box.minX + box.maxX) / 2
  const lipCenterY = (box.minY + box.maxY) / 2

  // 画布中心点
  const stageCenterX = stageW / 2
  const stageCenterY = stageH / 2

  // 计算偏移量，使唇线中心点缩放后正好居中到画布中心
  const offsetX = stageCenterX - lipCenterX * scale
  const offsetY = stageCenterY - lipCenterY * scale

  // 如果需要返回中心点而不是偏移量（用于某些组件）
  if (returnCenter && imgWidth && imgHeight) {
    const center = {
      x: offsetX + (imgWidth * scale) / 2,
      y: offsetY + (imgHeight * scale) / 2
    }
    return { scale, offsetX, offsetY, lipCenterX, lipCenterY, center }
  }

  // 返回缩放和偏移参数，以及唇线中心点
  return { scale, offsetX, offsetY, lipCenterX, lipCenterY }
}
