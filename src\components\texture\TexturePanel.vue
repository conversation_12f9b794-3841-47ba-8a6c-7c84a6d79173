<template>
  <div class="panel-card">
    <div class="panel-header">纹理选择</div>
    <div class="texture-grid">
      <div
        v-for="texture in textures"
        :key="texture.id"
        class="texture-item"
        :class="{ active: modelValue === texture.id }"
        @click="updateValue(texture.id)"
      >
        <img :src="texture.src" :alt="texture.name" class="texture-thumbnail" />
        <div class="texture-name">{{ texture.name }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { TextureInfo } from '@/composables/texture/useTextureManager'

// Props
const props = defineProps({
  textures: {
    type: Array as () => TextureInfo[],
    required: true
  },
  modelValue: {
    type: String,
    default: undefined
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 更新选中的纹理
function updateValue(id: string) {
  emit('update:modelValue', id)
}
</script>

<style scoped>
.panel-card {
  background: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.panel-header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #333;
}

.texture-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.texture-item {
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
  border: 2px solid transparent;
  transition: all 0.3s;
}

.texture-item.active {
  border-color: #409eff;
}

.texture-thumbnail {
  width: 100%;
  height: 26px;
  object-fit: cover;
}

.texture-name {
  font-size: 12px;
  text-align: center;
  padding: 4px 0;
}

.texture-opacity {
  margin-top: 16px;
}

.slider-label {
  font-size: 14px;
  margin-bottom: 8px;
}
</style>
