import type { ApiResponse } from './global'

// 用户实体
export interface User {
  id: number
  username: string
  nickname: string
  avatar: string
  roles: string[]
  permissions: string[]
}

// 登录参数
export interface LoginParams {
  username: string
  password: string
  remember?: boolean
}

// 登录结果
export interface LoginResult {
  token: string
  user: User
}

// API 响应类型
export type LoginResponse = ApiResponse<LoginResult | null>
export type UserResponse = ApiResponse<User>
export type VoidResponse = ApiResponse<void>
