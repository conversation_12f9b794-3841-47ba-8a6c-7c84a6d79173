<template>
  <div class="texture-test-page">
    <div class="page-header">
      <h1>纹理测试</h1>
    </div>

    <div class="content-container">
      <div class="canvas-container" ref="canvasContainerRef">
        <v-stage
          ref="stageRef"
          :config="stageConfig"
          @mousedown="handleStageMouseDown"
          @touchstart="handleStageMouseDown"
        >
          <!-- 背景层 -->
          <v-layer ref="bgLayerRef">
            <v-rect
              :config="{
                x: 0,
                y: 0,
                width: stageWidth,
                height: stageHeight,
                fill: '#f5f5f5'
              }"
            />
          </v-layer>

          <!-- 测试图形层 -->
          <v-layer ref="shapesLayerRef">
            <!-- 静态纹理闭合线条 -->
            <v-line
              ref="staticLineRef"
              :x="staticLinePos.x"
              :y="staticLinePos.y"
              :points="staticLinePoints"
              :config="{
                fill: 'white',
                stroke: '#4169e1',
                strokeWidth: 2,
                closed: true,
                tension: 0.4,
                draggable: true,
                ...staticTextureConfig
              }"
              @dragstart="handleStaticLineDragStart"
              @dragmove="handleStaticLineDragMove"
              @dragend="handleStaticLineDragEnd"
            />
          </v-layer>
        </v-stage>
      </div>

      <div class="controls-panel">
        <div class="panel-card">
          <div class="panel-header">纹理选择</div>
          <div class="texture-grid">
            <div
              v-for="texture in textures"
              :key="texture.id"
              class="texture-item"
              :class="{ active: selectedTextureId === texture.id }"
              @click="setTexture(texture.id)"
            >
              <img :src="texture.src" :alt="texture.name" class="texture-thumbnail" />
              <div class="texture-name">{{ texture.name }}</div>
            </div>
          </div>
          <div class="texture-opacity">
            <div class="slider-label">纹理强度</div>
            <el-slider
              v-model="textureStrength"
              :min="0"
              :max="1"
              :step="0.01"
              :format-tooltip="(val: number) => `${Math.round(val * 100)}%`"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useTextureManager } from '@/composables/texture/useTextureManager'

// 画布尺寸
const stageWidth = ref(1200)
const stageHeight = ref(800)

// 引用
const canvasContainerRef = ref<HTMLElement | null>(null)
const stageRef = ref<any>(null)
const bgLayerRef = ref<any>(null)
const shapesLayerRef = ref<any>(null)
const staticLineRef = ref<any>(null)

// 位置
const staticLinePos = ref({ x: 350, y: 300 })

// 创建线条点
const staticLinePoints = computed(() => {
  const points = []
  const numPoints = 12 // 控制点数量
  const radius = 200 // 半径

  for (let i = 0; i < numPoints; i++) {
    const angle = (i / numPoints) * Math.PI * 2
    const x = Math.cos(angle) * radius
    const y = Math.sin(angle) * radius
    points.push(x, y)
  }

  return points
})

// 获取纹理管理器
const {
  textures,
  selectedTextureId,
  colorAdjustments,
  selectedTextureImage,
  preloadTextures,
  setTexture,
  setTextureStrength
} = useTextureManager()

// 创建纹理强度的计算属性
const textureStrength = computed({
  get: () => colorAdjustments.value.textureStrength,
  set: (value) => setTextureStrength(value)
})

// 舞台配置
const stageConfig = computed(() => {
  return {
    width: stageWidth.value,
    height: stageHeight.value
  }
})

// 静态纹理配置（相对于画布静止）
const staticTextureConfig = computed(() => {
  if (!selectedTextureImage.value) return {}

  return {
    fillPatternImage: selectedTextureImage.value,
    fillPatternOpacity: textureStrength.value,
    fillPatternScale: { x: 0.7, y: 0.7 },
    fillPatternOffset: { x: 1100, y: 260 },
    // fillPatternX: -200,
    // fillPatternY: -200,
    fillPatternRepeat: 'no-repeat',
    fillPriority: 'pattern'
  }
})

// 事件处理函数
function handleStageMouseDown(e: any) {
  // 如果点击的是舞台背景，取消选中所有图形
  if (e.target === e.target.getStage()) {
    console.log('点击舞台背景')
  }
}

// 静态线条拖动
function handleStaticLineDragStart() {
  console.log('静态线条开始拖动')
}

function handleStaticLineDragMove(e: any) {
  // staticLinePos.value = {
  //   x: e.target.x(),
  //   y: e.target.y()
  // }
  updateStaticLineTexture()
}

function handleStaticLineDragEnd() {
  console.log('静态线条结束拖动，位置:', staticLinePos.value)
}

// 更新静态纹理
function updateStaticLineTexture() {
  if (!staticLineRef.value) return

  try {
    const line = staticLineRef.value.getNode()
    if (line) {
      // 更新纹理偏移
      // line.fillPatternOffset({
      //   x: -staticLinePos.value.x,
      //   y: -staticLinePos.value.y
      // })

      // 重新绘制
      const layer = line.getLayer()
      if (layer) {
        layer.batchDraw()
      }
    }
  } catch (error) {
    console.error('更新静态纹理时出错:', error)
  }
}

// 在组件挂载时初始化
onMounted(async () => {
  // 预加载纹理
  await preloadTextures()

  // 调整画布大小
  if (canvasContainerRef.value) {
    const containerWidth = canvasContainerRef.value.clientWidth
    const containerHeight = canvasContainerRef.value.clientHeight

    if (containerWidth > 0 && containerHeight > 0) {
      stageWidth.value = containerWidth
      stageHeight.value = containerHeight
    }
  }
})
</script>

<style scoped>
.texture-test-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.content-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.controls-panel {
  width: 300px;
  padding: 10px;
  background-color: #f9f9f9;
  border-left: 1px solid #eee;
  overflow-y: auto;
}

.panel-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
  overflow: hidden;
}

.panel-header {
  padding: 10px 15px;
  font-weight: bold;
  background-color: #f5f5f5;
  border-bottom: 1px solid #eee;
}

.texture-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  padding: 15px;
}

.texture-item {
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
  border: 2px solid transparent;
  transition: all 0.2s;
}

.texture-item.active {
  border-color: #409eff;
}

.texture-thumbnail {
  width: 100%;
  height: 80px;
  object-fit: cover;
  display: block;
}

.texture-name {
  text-align: center;
  padding: 5px;
  font-size: 12px;
}

.texture-opacity {
  padding: 0 15px 15px;
}

.slider-label {
  margin-bottom: 10px;
  font-size: 14px;
}
</style>
