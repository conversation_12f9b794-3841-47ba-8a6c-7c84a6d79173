<template>
  <div class="panel-card">
    <div class="panel-header">阴影效果</div>

    <!-- 启用阴影开关 -->
    <!-- <div class="switch-container">
      <span class="switch-label">启用阴影</span>
      <el-switch v-model="enabledValue" />
    </div> -->

    <!-- 阴影强度 -->
    <div class="slider-container">
      <!-- <div class="slider-label">阴影强度</div> -->
      <el-slider
        v-model="intensityValue"
        :min="0"
        :max="1"
        :step="0.01"
        :format-tooltip="(val: number) => `${Math.round(val * 100)}%`"
      />
    </div>

    <!-- 阴影范围 -->
    <!-- <div class="slider-container">
      <div class="slider-label">阴影范围</div>
      <el-slider
        v-model="rangeValue"
        :min="1"
        :max="20"
        :step="1"
        :disabled="!enabledValue"
        :format-tooltip="(val: number) => `${val}px`"
      />
    </div> -->

    <!-- 重置按钮 -->
    <!-- <div class="reset-button-container">
      <el-button size="small" @click="resetValues">重置阴影</el-button>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Props
const props = defineProps({
  enabled: {
    type: Boolean,
    default: true
  },
  intensity: {
    type: Number,
    default: 0.3
  },
  range: {
    type: Number,
    default: 5
  }
})

// Emits
const emit = defineEmits(['update:enabled', 'update:intensity', 'update:range'])

// 计算属性 - 用于v-model双向绑定
// const enabledValue = computed({
//   get: () => props.enabled,
//   set: (value) => emit('update:enabled', value)
// })

const intensityValue = computed({
  get: () => props.intensity,
  set: (value) => emit('update:intensity', value)
})

// const rangeValue = computed({
//   get: () => props.range,
//   set: (value) => emit('update:range', value)
// })

// 重置所有值
// function resetValues() {
//   emit('update:enabled', true)
//   emit('update:intensity', 0.3)
//   emit('update:range', 5)
// }
</script>

<style scoped>
.panel-card {
  background: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.panel-header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 6px;
  color: #333;
}

.switch-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.switch-label {
  font-size: 14px;
}

.slider-container {
  margin-bottom: 0;
}

.slider-container.disabled {
  opacity: 0.6;
}

.slider-label {
  font-size: 14px;
  margin-bottom: 8px;
}

.reset-button-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

:deep(.el-checkbox) {
  height: 20px;
}

:deep(.el-slider__runway) {
  margin: 0;
  flex: 1;
}

:deep(.el-slider__bar) {
  border-radius: 3px;
  height: 6px;
  background-color: transparent;
}

:deep(.el-slider__runway) {
  height: 6px;
  background: linear-gradient(to left, #797878, #e6e6e6);
}

:deep(.el-slider__button) {
  border-color: #0052d9;
  box-shadow: 0 0 2px rgba(0, 82, 217, 0.5);
  width: 14px;
  height: 14px;
}
</style>
