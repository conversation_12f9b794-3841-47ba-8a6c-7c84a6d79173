<template>
  <v-group>
    <!-- 垂直分割线 -->
    <v-group ref="dividerGroup" name="dividers" :visible="visible"></v-group>

    <!-- 上曲线 -->
    <v-line
      ref="curve1"
      name="curve1"
      :points="topCurvePoints"
      :stroke="topCurveColor"
      :strokeWidth="CURVE_STROKE_WIDTH"
      :tension="tensionFactor"
      lineCap="round"
      :visible="visible"
    />

    <!-- 下曲线 -->
    <v-line
      ref="curve2"
      name="curve2"
      :points="bottomCurvePoints"
      :stroke="bottomCurveColor"
      :strokeWidth="CURVE_STROKE_WIDTH"
      :tension="tensionFactor"
      lineCap="round"
      :visible="visible"
    />
  </v-group>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import Konva from 'konva'
import { useFrameCurves } from '@/composables/veneer/frame/useFrameCurves'
import { useFrameDividers } from '@/composables/veneer/frame/useFrameDividers'
import { useTextureEditorContext } from '@/contexts/TextureEditorContext'

// 声明window.Konva，避免TypeScript错误
declare global {
  interface Window {
    Konva: typeof Konva
  }
}

// Props
const props = defineProps({
  stageScale: {
    type: Number,
    default: 1
  },
  visible: {
    type: Boolean,
    default: true
  }
})

// 获取纹理编辑器上下文
const editorContext = useTextureEditorContext()

// 使用局部框架位置
const frame = computed(() => ({
  x: editorContext.localFrameX.value,
  y: editorContext.localFrameY.value,
  width: editorContext.localFrameWidth.value,
  height: editorContext.localFrameHeight.value,
  topCurvePoints: editorContext.localFrameTopCurvePoints.value,
  bottomCurvePoints: editorContext.localFrameBottomCurvePoints.value
}))

// 常量定义
const CURVE_STROKE_WIDTH = 2.5

// 颜色定义
const topCurveColor = '#007bff'
const bottomCurveColor = '#28a745'

// 引用
const dividerGroup = ref<any>(null)
const curve1 = ref<any>(null)
const curve2 = ref<any>(null)

// 拖动状态 - 用于优化性能
const dragUpdateScheduled = ref(false)

// 使用框架曲线管理 composable
const {
  tensionFactor,
  currentStartX,
  currentEndX,
  topCurveEndpointY,
  topCurveMidY,
  bottomCurveEndpointY,
  bottomCurveMidY,
  topCurvePoints,
  bottomCurvePoints
} = useFrameCurves(frame, dragUpdateScheduled)

// 使用框架分割线管理 composable
const { updateVerticalLines } = useFrameDividers(
  dividerGroup,
  curve1,
  curve2,
  frame,
  topCurveEndpointY,
  bottomCurveEndpointY,
  currentStartX,
  currentEndX,
  topCurveMidY,
  bottomCurveMidY,
  tensionFactor
)

// 监听框架变化和缩放比例变化，更新垂直分割线
watch(
  [frame, () => props.stageScale],
  () => {
    // 使用requestAnimationFrame优化性能
    if (!dragUpdateScheduled.value) {
      dragUpdateScheduled.value = true
      requestAnimationFrame(() => {
        updateVerticalLines()
        dragUpdateScheduled.value = false
      })
    }
  },
  { deep: true }
)

onMounted(() => {
  updateVerticalLines()
})
</script>
