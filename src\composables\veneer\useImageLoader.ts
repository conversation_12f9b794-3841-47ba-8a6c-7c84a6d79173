import { ref, shallowRef, watch, markRaw } from 'vue'
import { useCommonStore } from '@/store/common'
import { storeToRefs } from 'pinia'

/**
 * 处理图片加载和图片对象管理
 */
export function useImageLoader() {
  // 从store获取数据
  const commonStore = useCommonStore()
  const { smileImage, mouthImage } = storeToRefs(commonStore)

  // 图片对象 - 使用shallowRef减少不必要的深度响应式监听
  const smileImgObj = shallowRef<HTMLImageElement | null>(null)
  const mouthImgObj = shallowRef<HTMLImageElement | null>(null)

  // 图片原始尺寸
  const smileImgNaturalWidth = ref(0)
  const smileImgNaturalHeight = ref(0)
  const mouthImgNaturalWidth = ref(0)
  const mouthImgNaturalHeight = ref(0)

  // 加载微笑照
  watch(
    () => smileImage.value,
    (src) => {
      if (!src) return
      const img = new window.Image()
      img.src = src
      img.onload = () => {
        // 使用markRaw标记图片对象，避免Vue对其进行响应式处理
        smileImgObj.value = markRaw(img)
        smileImgNaturalWidth.value = img.width
        smileImgNaturalHeight.value = img.height
      }
    },
    { immediate: true }
  )

  // 加载开口照
  watch(
    () => mouthImage.value,
    (src) => {
      if (!src) return
      const img = new window.Image()
      img.src = src
      img.onload = () => {
        // 使用markRaw标记图片对象，避免Vue对其进行响应式处理
        mouthImgObj.value = markRaw(img)
        mouthImgNaturalWidth.value = img.width
        mouthImgNaturalHeight.value = img.height
      }
    },
    { immediate: true }
  )

  return {
    smileImgObj,
    mouthImgObj,
    smileImgNaturalWidth,
    smileImgNaturalHeight,
    mouthImgNaturalWidth,
    mouthImgNaturalHeight
  }
}
