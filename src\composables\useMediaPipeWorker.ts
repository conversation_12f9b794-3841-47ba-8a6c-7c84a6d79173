import { ref } from 'vue'

/**
 * MediaPipe Web Worker 管理器
 * 提供在后台线程中执行面部检测的功能
 */
export function useMediaPipeWorker() {
  const worker = ref<Worker | null>(null)
  const isInitialized = ref(false)
  const isInitializing = ref(false)
  
  // 消息ID计数器，用于匹配请求和响应
  let messageId = 0
  const pendingMessages = new Map<number, { resolve: Function; reject: Function }>()

  /**
   * 初始化Worker
   */
  async function initializeWorker(): Promise<boolean> {
    if (worker.value && isInitialized.value) return true
    if (isInitializing.value) {
      // 等待初始化完成
      while (isInitializing.value) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      return isInitialized.value
    }

    try {
      isInitializing.value = true
      console.log('正在初始化MediaPipe Worker...')

      // 创建Worker
      worker.value = new Worker('/mediapipe-worker.js')
      
      // 设置消息处理器
      worker.value.onmessage = handleWorkerMessage
      worker.value.onerror = handleWorkerError

      // 初始化MediaPipe检测器
      const initResult = await sendMessage('INIT', {})
      
      if (initResult.success) {
        isInitialized.value = true
        console.log('MediaPipe Worker初始化成功')
        return true
      } else {
        throw new Error('Worker初始化失败')
      }
    } catch (error) {
      console.error('MediaPipe Worker初始化失败:', error)
      disposeWorker()
      return false
    } finally {
      isInitializing.value = false
    }
  }

  /**
   * 处理Worker消息
   */
  function handleWorkerMessage(e: MessageEvent) {
    const { type, data, id } = e.data

    if (id && pendingMessages.has(id)) {
      const { resolve } = pendingMessages.get(id)!
      pendingMessages.delete(id)
      
      if (type === 'ERROR') {
        const { reject } = pendingMessages.get(id) || { reject: () => {} }
        reject(new Error(data.error))
      } else {
        resolve(data)
      }
    }
  }

  /**
   * 处理Worker错误
   */
  function handleWorkerError(error: ErrorEvent) {
    console.error('MediaPipe Worker错误:', error)
    
    // 拒绝所有待处理的消息
    pendingMessages.forEach(({ reject }) => {
      reject(new Error('Worker错误'))
    })
    pendingMessages.clear()
    
    disposeWorker()
  }

  /**
   * 向Worker发送消息
   */
  function sendMessage(type: string, data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!worker.value) {
        reject(new Error('Worker未初始化'))
        return
      }

      const id = ++messageId
      pendingMessages.set(id, { resolve, reject })

      worker.value.postMessage({ type, data, id })

      // 设置超时
      setTimeout(() => {
        if (pendingMessages.has(id)) {
          pendingMessages.delete(id)
          reject(new Error('Worker消息超时'))
        }
      }, 30000) // 30秒超时
    })
  }

  /**
   * 从图片元素检测面部关键点
   */
  async function detectFaceFromImage(imageElement: HTMLImageElement, label: string = '图片'): Promise<any> {
    try {
      // 确保Worker已初始化
      const initialized = await initializeWorker()
      if (!initialized) {
        throw new Error('Worker初始化失败')
      }

      console.log(`开始在Worker中检测${label}...`)

      // 将图片转换为ImageData
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      
      canvas.width = imageElement.naturalWidth
      canvas.height = imageElement.naturalHeight
      ctx.drawImage(imageElement, 0, 0)
      
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)

      // 发送检测请求到Worker
      const result = await sendMessage('DETECT', {
        imageData: Array.from(imageData.data), // 转换为普通数组以便传输
        width: canvas.width,
        height: canvas.height,
        label
      })

      console.log(`${label}检测完成`)
      return result.result

    } catch (error) {
      console.error(`${label}检测失败:`, error)
      throw error
    }
  }

  /**
   * 检测单张图片（从URL）
   */
  async function detectSingleImage(imageSrc: string, label: string): Promise<any> {
    try {
      console.log(`开始检测${label}...`)
      
      // 创建图片元素
      const img = new Image()
      img.crossOrigin = 'anonymous'

      // 等待图片加载
      await new Promise<void>((resolve, reject) => {
        img.onload = () => resolve()
        img.onerror = () => reject(new Error(`${label}加载失败`))
        img.src = imageSrc
      })

      // 进行检测
      const result = await detectFaceFromImage(img, label)
      
      if (result) {
        console.log(`${label}检测成功`)
        return result
      } else {
        console.warn(`${label}未检测到面部`)
        return null
      }
    } catch (error) {
      console.error(`${label}检测失败:`, error)
      return null
    }
  }

  /**
   * 销毁Worker
   */
  async function disposeWorker() {
    if (worker.value) {
      try {
        // 通知Worker清理资源
        await sendMessage('DISPOSE', {})
      } catch (error) {
        console.warn('Worker清理失败:', error)
      }
      
      worker.value.terminate()
      worker.value = null
      isInitialized.value = false
      
      // 清理待处理的消息
      pendingMessages.clear()
      
      console.log('MediaPipe Worker已销毁')
    }
  }

  /**
   * 检查Worker是否就绪
   */
  function isWorkerReady(): boolean {
    return !!worker.value && isInitialized.value && !isInitializing.value
  }

  return {
    // 状态
    isInitialized,
    isInitializing,
    
    // 方法
    initializeWorker,
    detectFaceFromImage,
    detectSingleImage,
    disposeWorker,
    isWorkerReady
  }
}
