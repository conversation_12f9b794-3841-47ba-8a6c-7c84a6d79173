<template>
  <v-group>
    <!-- 垂直分割线 -->
    <v-group ref="dividerGroup" name="dividers"></v-group>

    <!-- 上曲线 -->
    <v-line
      ref="curve1"
      name="curve1"
      :points="topCurvePoints"
      :stroke="topCurveColor"
      :strokeWidth="CURVE_STROKE_WIDTH"
      :tension="tensionFactor"
      lineCap="round"
    />

    <!-- 下曲线 -->
    <v-line
      ref="curve2"
      name="curve2"
      :points="bottomCurvePoints"
      :stroke="bottomCurveColor"
      :strokeWidth="CURVE_STROKE_WIDTH"
      :tension="tensionFactor"
      lineCap="round"
    />

    <!-- 上曲线端点控制点 -->
    <v-circle
      ref="leftAnchor1"
      name="leftAnchor1"
      :x="currentStartX"
      :y="topCurveEndpointY"
      :radius="10"
      :fill="topCurveColor"
      :stroke="topCurveColor"
      :strokeWidth="2"
      :config="{
        draggable: true,
        hitStrokeWidth: 10,
        dragBoundFunc: leftAnchor1DragBoundFunc
      }"
      @dragmove="handleLeftAnchor1Drag"
      @mouseenter="handleAnchorMouseEnter"
      @mouseleave="handleAnchorMouseLeave"
    />
    <v-circle
      ref="rightAnchor1"
      name="rightAnchor1"
      :x="currentEndX"
      :y="topCurveEndpointY"
      :radius="10"
      :fill="topCurveColor"
      :stroke="topCurveColor"
      :strokeWidth="2"
      :config="{
        draggable: true,
        hitStrokeWidth: 10,
        dragBoundFunc: rightAnchor1DragBoundFunc
      }"
      @dragmove="handleRightAnchor1Drag"
      @mouseenter="handleAnchorMouseEnter"
      @mouseleave="handleAnchorMouseLeave"
    />

    <!-- 下曲线端点控制点 -->
    <v-circle
      ref="leftAnchor2"
      name="leftAnchor2"
      :x="currentStartX"
      :y="bottomCurveEndpointY"
      :radius="10"
      :fill="bottomCurveColor"
      :stroke="bottomCurveColor"
      :strokeWidth="2"
      :config="{
        draggable: true,
        hitStrokeWidth: 10,
        dragBoundFunc: leftAnchor2DragBoundFunc
      }"
      @dragmove="handleLeftAnchor2Drag"
      @mouseenter="handleAnchorMouseEnter"
      @mouseleave="handleAnchorMouseLeave"
    />
    <v-circle
      ref="rightAnchor2"
      name="rightAnchor2"
      :x="currentEndX"
      :y="bottomCurveEndpointY"
      :radius="10"
      :fill="bottomCurveColor"
      :stroke="bottomCurveColor"
      :strokeWidth="2"
      :config="{
        draggable: true,
        hitStrokeWidth: 10,
        dragBoundFunc: rightAnchor2DragBoundFunc
      }"
      @dragmove="handleRightAnchor2Drag"
      @mouseenter="handleAnchorMouseEnter"
      @mouseleave="handleAnchorMouseLeave"
    />

    <!-- 上曲线中点控制点 -->
    <v-image
      ref="midAnchor1"
      name="midAnchor1"
      :x="currentMidX"
      :y="topCurveMidY"
      :image="moveVIconObj"
      :width="28"
      :height="28"
      :offsetX="14"
      :offsetY="14"
      :config="{
        draggable: true,
        dragBoundFunc: midAnchor1DragBoundFunc
      }"
      @dragstart="handleMidAnchor1DragStart"
      @dragmove="handleMidAnchor1Drag"
      @mouseenter="handleIconMouseEnter"
      @mouseleave="handleIconMouseLeave"
    />

    <!-- 下曲线中点控制点 -->
    <v-image
      ref="midAnchor2"
      name="midAnchor2"
      :x="currentMidX"
      :y="bottomCurveMidY"
      :image="moveVIconObj"
      :width="28"
      :height="28"
      :offsetX="14"
      :offsetY="14"
      :config="{
        draggable: true,
        dragBoundFunc: midAnchor2DragBoundFunc
      }"
      @dragstart="handleMidAnchor2DragStart"
      @dragmove="handleMidAnchor2Drag"
      @mouseenter="handleIconMouseEnter"
      @mouseleave="handleIconMouseLeave"
    />

    <!-- 宽度控制锚点 -->
    <v-image
      ref="leftWidthAnchor"
      name="leftWidthAnchor"
      :x="currentStartX"
      :y="widthAnchorsY"
      :image="moveHIconObj"
      :width="28"
      :height="16"
      :offsetX="28"
      :offsetY="8"
      :config="{
        draggable: true,
        dragBoundFunc: leftWidthAnchorDragBoundFunc
      }"
      @dragmove="handleLeftWidthAnchorDrag"
      @mouseenter="handleIconMouseEnter"
      @mouseleave="handleIconMouseLeave"
    />
    <v-image
      ref="rightWidthAnchor"
      name="rightWidthAnchor"
      :x="currentEndX"
      :y="widthAnchorsY"
      :image="moveHIconObj"
      :width="28"
      :height="16"
      :offsetX="0"
      :offsetY="8"
      :config="{
        draggable: true,
        dragBoundFunc: rightWidthAnchorDragBoundFunc
      }"
      @dragmove="handleRightWidthAnchorDrag"
      @mouseenter="handleIconMouseEnter"
      @mouseleave="handleIconMouseLeave"
    />

    <!-- 整体拖动主控制锚点 -->
    <v-image
      ref="masterAnchor"
      name="masterAnchor"
      :x="currentMidX"
      :y="masterAnchorY"
      :image="moveIconObj"
      :width="32"
      :height="32"
      :offsetX="16"
      :offsetY="16"
      :config="{
        draggable: true
      }"
      @dragstart="handleMasterAnchorDragStart"
      @dragmove="handleMasterAnchorDrag"
      @mouseenter="handleIconMouseEnter"
      @mouseleave="handleIconMouseLeave"
    />
  </v-group>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, onActivated, watch } from 'vue'
import { useTeethStore } from '@/store/teeth'
import Konva from 'konva'
import moveIcon from '@/assets/images/move.svg'
import moveHIcon from '@/assets/images/move-h.svg'
import moveVIcon from '@/assets/images/move-v.svg'
import { useFrameCurves } from '@/composables/veneer/frame/useFrameCurves'
import { useFrameDividers } from '@/composables/veneer/frame/useFrameDividers'
import {
  useFrameAnchors,
  createCurveMidDragBoundFunc,
  createCurveEndpointDragBoundFunc,
  createWidthAnchorDragBoundFunc
} from '@/composables/veneer/frame/useFrameAnchors'
import { useFrameIcons } from '@/composables/veneer/frame/useFrameIcons'

// 声明window.Konva，避免TypeScript错误
declare global {
  interface Window {
    Konva: typeof Konva
  }
}

// Props
const props = defineProps({
  stageScale: {
    type: Number,
    default: 1
  }
})

// Store
const teethStore = useTeethStore()
const frame = computed(() => teethStore.frame)

// 常量定义
const CURVE_STROKE_WIDTH = 2.5

// 颜色定义
const topCurveColor = '#007bff'
const bottomCurveColor = '#28a745'

// 引用
const dividerGroup = ref<any>(null)
const curve1 = ref<any>(null)
const curve2 = ref<any>(null)
const leftAnchor1 = ref<any>(null)
const rightAnchor1 = ref<any>(null)
const leftAnchor2 = ref<any>(null)
const rightAnchor2 = ref<any>(null)
const midAnchor1 = ref<any>(null)
const midAnchor2 = ref<any>(null)
const leftWidthAnchor = ref<any>(null)
const rightWidthAnchor = ref<any>(null)
const masterAnchor = ref<any>(null)

// 拖动状态
const dragUpdateScheduled = ref(false)

// 使用框架曲线管理 composable
const {
  tensionFactor,
  currentMidX,
  currentStartX,
  currentEndX,
  topCurveEndpointY,
  topCurveMidY,
  bottomCurveEndpointY,
  bottomCurveMidY,
  widthAnchorsY,
  masterAnchorY,
  topCurvePoints,
  bottomCurvePoints,
  handleLeftAnchor1Drag,
  handleRightAnchor1Drag,
  handleLeftAnchor2Drag,
  handleRightAnchor2Drag
} = useFrameCurves(frame, dragUpdateScheduled)

// 使用框架分割线管理 composable
const { updateVerticalLines } = useFrameDividers(
  dividerGroup,
  curve1,
  curve2,
  frame,
  topCurveEndpointY,
  bottomCurveEndpointY,
  currentStartX,
  currentEndX,
  topCurveMidY,
  bottomCurveMidY,
  tensionFactor
)

// 使用框架锚点管理 composable
const {
  handleAnchorMouseEnter,
  handleAnchorMouseLeave,
  handleIconMouseEnter,
  handleIconMouseLeave,
  handleMidAnchor1DragStart,
  handleMidAnchor1Drag,
  handleMidAnchor2DragStart,
  handleMidAnchor2Drag,
  handleLeftWidthAnchorDrag,
  handleRightWidthAnchorDrag,
  handleMasterAnchorDragStart,
  handleMasterAnchorDrag
} = useFrameAnchors(frame, dragUpdateScheduled, currentMidX, topCurveMidY)

// 使用框架图标管理 composable
const { moveIconObj, moveHIconObj, moveVIconObj } = useFrameIcons(moveIcon, moveHIcon, moveVIcon)

// 创建上曲线中点拖动边界函数
const midAnchor1DragBoundFunc = createCurveMidDragBoundFunc(
  true, // 上曲线
  frame,
  currentMidX,
  topCurveMidY,
  bottomCurveMidY
)

// 创建下曲线中点拖动边界函数
const midAnchor2DragBoundFunc = createCurveMidDragBoundFunc(
  false, // 下曲线
  frame,
  currentMidX,
  topCurveMidY,
  bottomCurveMidY
)

// 创建上曲线左端点拖动边界函数
const leftAnchor1DragBoundFunc = createCurveEndpointDragBoundFunc(
  true, // 上曲线
  currentStartX,
  topCurveEndpointY,
  bottomCurveEndpointY
)

// 创建上曲线右端点拖动边界函数
const rightAnchor1DragBoundFunc = createCurveEndpointDragBoundFunc(
  true, // 上曲线
  currentEndX,
  topCurveEndpointY,
  bottomCurveEndpointY
)

// 创建下曲线左端点拖动边界函数
const leftAnchor2DragBoundFunc = createCurveEndpointDragBoundFunc(
  false, // 下曲线
  currentStartX,
  topCurveEndpointY,
  bottomCurveEndpointY
)

// 创建下曲线右端点拖动边界函数
const rightAnchor2DragBoundFunc = createCurveEndpointDragBoundFunc(
  false, // 下曲线
  currentEndX,
  topCurveEndpointY,
  bottomCurveEndpointY
)

// 创建左侧宽度控制锚点拖动边界函数
const leftWidthAnchorDragBoundFunc = createWidthAnchorDragBoundFunc(
  true, // 左侧锚点
  currentMidX,
  widthAnchorsY
)

// 创建右侧宽度控制锚点拖动边界函数
const rightWidthAnchorDragBoundFunc = createWidthAnchorDragBoundFunc(
  false, // 右侧锚点
  currentMidX,
  widthAnchorsY
)

// 监听框架变化和缩放比例变化，更新垂直分割线
watch(
  [frame, () => props.stageScale],
  () => {
    // 使用requestAnimationFrame优化性能
    if (!dragUpdateScheduled.value) {
      dragUpdateScheduled.value = true
      requestAnimationFrame(() => {
        updateVerticalLines()
        dragUpdateScheduled.value = false
      })
    }
  },
  { deep: true }
)

// 组件挂载后初始化
onMounted(() => {
  // 立即执行一次更新
  updateVerticalLines()

  // 延迟执行updateVerticalLines，确保曲线节点已经准备好
  setTimeout(() => {
    updateVerticalLines()
  }, 100)

  // 监听窗口大小改变事件，更新垂直分割线
  const handleResize = () => {
    if (!dragUpdateScheduled.value) {
      dragUpdateScheduled.value = true
      requestAnimationFrame(() => {
        updateVerticalLines()
        dragUpdateScheduled.value = false
      })
    }
  }

  window.addEventListener('resize', handleResize)

  // 在组件卸载时移除事件监听器
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })
})

onActivated(() => {
  updateVerticalLines()
})
</script>
