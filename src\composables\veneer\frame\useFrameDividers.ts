import { Ref, ComputedRef } from 'vue'
import { useTeethStore, SEGMENT_WIDTH_RATIOS_CENTER_OUT, TeethFrame } from '@/store/teeth'
import { getYAtX } from '@/utils/curveUtils'

/**
 * 框架分割线管理的组合式函数
 * 处理框架垂直分割线的渲染和计算
 */
export function useFrameDividers(
  dividerGroup: Ref<any>,
  curve1: Ref<any>,
  curve2: Ref<any>,
  frame: ComputedRef<TeethFrame>,
  topCurveEndpointY: ComputedRef<number>,
  bottomCurveEndpointY: ComputedRef<number>,
  currentStartX: ComputedRef<number>,
  currentEndX: ComputedRef<number>,
  topCurveMidY: ComputedRef<number>,
  bottomCurveMidY: ComputedRef<number>,
  tensionFactor: number
) {
  const teethStore = useTeethStore()

  // 分割线颜色
  const dividerLineColor = '#aaaaaa'

  /**
   * 更新垂直分割线
   */
  function updateVerticalLines() {
    if (!dividerGroup.value) return

    const dividerNode = dividerGroup.value.getNode()
    if (!dividerNode) return

    // 清除旧的分割线
    dividerNode.destroyChildren()

    const totalWidth = frame.value.width
    if (totalWidth <= 0) return

    // 使用从store导入的常量
    const ratiosFromEdgeToCenter = [...SEGMENT_WIDTH_RATIOS_CENTER_OUT].reverse()
    const allSegmentRatios = [...ratiosFromEdgeToCenter, ...SEGMENT_WIDTH_RATIOS_CENTER_OUT]

    const sumOfRatioUnits = allSegmentRatios.reduce((sum, ratio) => sum + ratio, 0)
    if (sumOfRatioUnits === 0) return

    const baseUnitWidth = totalWidth / sumOfRatioUnits

    let accumulatedX = frame.value.x

    // 记录当前曲线状态，用于后续计算
    const currentCurve1 = curve1.value?.getNode()
    const currentCurve2 = curve2.value?.getNode()

    // 收集所有分割线的X坐标，用于计算区块信息
    const dividerXPositions: number[] = [accumulatedX]

    // 绘制最左侧的边界线
    // 使用Konva.Line构造函数创建线条
    const leftBoundaryLine = new window.Konva.Line({
      points: [accumulatedX, topCurveEndpointY.value, accumulatedX, bottomCurveEndpointY.value],
      stroke: dividerLineColor,
      strokeWidth: 2,
      dash: [4, 4],
      listening: false
    })
    dividerNode.add(leftBoundaryLine)

    // 绘制内部分割线和最右侧边界线
    for (let i = 0; i < allSegmentRatios.length; i++) {
      const segmentRatio = allSegmentRatios[i] || 0
      accumulatedX += segmentRatio * baseUnitWidth

      // 添加到分割线位置数组
      dividerXPositions.push(accumulatedX)

      // 计算分割线的Y坐标
      let y1, y2

      if (i === allSegmentRatios.length - 1) {
        // 最右侧边界线
        y1 = topCurveEndpointY.value
        y2 = bottomCurveEndpointY.value
      } else {
        // 内部分割线，使用曲线上的点
        // 使用之前记录的曲线状态
        if (currentCurve1 && currentCurve2) {
          y1 = getYAtX(currentCurve1, accumulatedX, tensionFactor) || topCurveEndpointY.value
          y2 = getYAtX(currentCurve2, accumulatedX, tensionFactor) || bottomCurveEndpointY.value
        } else {
          // 曲线节点不可用，使用线性插值
          const ratio =
            (accumulatedX - currentStartX.value) / (currentEndX.value - currentStartX.value)
          y1 = topCurveEndpointY.value + ratio * (topCurveMidY.value - topCurveEndpointY.value) * 2
          y2 =
            bottomCurveEndpointY.value +
            ratio * (bottomCurveMidY.value - bottomCurveEndpointY.value) * 2
        }
      }

      // 创建内部分割线
      const dividerLine = new window.Konva.Line({
        points: [accumulatedX, y1, accumulatedX, y2],
        stroke: dividerLineColor,
        strokeWidth: 2,
        dash: [4, 4],
        listening: false
      })
      dividerNode.add(dividerLine)
    }

    // 确保分割线在最底层
    dividerNode.moveToBottom()

    // 直接计算分割区块信息并更新到store
    const newSegments = []
    const midX = frame.value.x + frame.value.width / 2

    // 遍历所有分割线位置，创建区块信息
    for (let i = 0; i < dividerXPositions.length - 1; i++) {
      const startX = dividerXPositions[i] || 0
      const endX = dividerXPositions[i + 1] || 0
      const width = endX - startX

      // 获取上下曲线在分割线位置的Y坐标
      let topY1, topY2, bottomY1, bottomY2

      if (currentCurve1 && currentCurve2) {
        topY1 = getYAtX(currentCurve1, startX, tensionFactor) || topCurveEndpointY.value
        topY2 = getYAtX(currentCurve1, endX, tensionFactor) || topCurveEndpointY.value
        bottomY1 = getYAtX(currentCurve2, startX, tensionFactor) || bottomCurveEndpointY.value
        bottomY2 = getYAtX(currentCurve2, endX, tensionFactor) || bottomCurveEndpointY.value
      } else {
        // 曲线节点不可用，使用线性插值
        const ratio1 = (startX - currentStartX.value) / (currentEndX.value - currentStartX.value)
        const ratio2 = (endX - currentStartX.value) / (currentEndX.value - currentStartX.value)

        topY1 =
          topCurveEndpointY.value + ratio1 * (topCurveMidY.value - topCurveEndpointY.value) * 2
        topY2 =
          topCurveEndpointY.value + ratio2 * (topCurveMidY.value - topCurveEndpointY.value) * 2
        bottomY1 =
          bottomCurveEndpointY.value +
          ratio1 * (bottomCurveMidY.value - bottomCurveEndpointY.value) * 2
        bottomY2 =
          bottomCurveEndpointY.value +
          ratio2 * (bottomCurveMidY.value - bottomCurveEndpointY.value) * 2
      }

      // 使用平均值作为区块的上下边界
      const topY = (topY1 + topY2) / 2
      const bottomY = (bottomY1 + bottomY2) / 2
      const height = bottomY - topY

      // 计算区块中心点
      const centerX = startX + width / 2
      const centerY = topY + height / 2

      // 确定区块是否在中心线左侧
      const isLeft = centerX < midX

      // 计算区块索引（确保唯一性）
      // 左侧使用负数索引，右侧使用正数索引，确保不会有重复
      const index = isLeft
        ? -(Math.floor(dividerXPositions.length / 2) - i)
        : i - Math.floor(dividerXPositions.length / 2) + 1

      newSegments.push({
        index,
        startX,
        endX,
        width,
        topY,
        bottomY,
        height,
        centerX,
        centerY,
        isLeft
      })
    }

    // 记录更新日志，帮助调试
    console.log('框架分割线更新，segments将更新', {
      segmentsCount: newSegments.length,
      frameX: frame.value.x,
      frameY: frame.value.y,
      frameWidth: frame.value.width,
      frameHeight: frame.value.height
    })

    // 直接更新store中的segments
    teethStore.$patch({ segments: newSegments })
  }

  return {
    dividerLineColor,
    updateVerticalLines
  }
}
