import { computed } from 'vue'
import { useCommonStore } from '@/store/common'
import { storeToRefs } from 'pinia'
import { calcFaceRotation } from '@/utils/faceMath'
import type { FacePoint } from '@/types/face'

/**
 * 面部数据管理的组合式函数
 * 提供面部关键点、唇线点、对齐点等数据的管理功能
 * 不关心数据的来源（可以是AI检测或手动设置）
 */
export function useFaceData() {
  // 从store获取数据
  const commonStore = useCommonStore()
  const {
    smileFaceLandmarks: storeFaceLandmarks,
    faceRotation: storeFaceRotation,
    smileLipPoints: storeSmileLipPoints,
    mouthLipPoints: storeMouthLipPoints,
    originalSmileLipPoints: storeOriginalSmileLipPoints,
    smileAlignPoints: storeSmileAlignPoints,
    mouthAlignPoints: storeMouthAlignPoints,
    initialSmileAlignPoints: storeInitialSmileAlignPoints,
    initialMouthAlignPoints: storeInitialMouthAlignPoints,
    mouthToSmileTransform: storeMouthToSmileTransform
  } = storeToRefs(commonStore)

  /**
   * 只设置微笑照面部关键点
   * @param points 微笑照面部关键点数组
   */
  function setSmileFaceLandmarks(points: FacePoint[]) {
    // 设置面部关键点
    storeFaceLandmarks.value = points

    // 计算并设置面部旋转角度
    storeFaceRotation.value = calcFaceRotation(points)
  }

  /**
   * 设置所有面部数据（面部关键点、唇线点、对齐点）
   * @param points 微笑照面部关键点数组
   * @param smileLipPoints 微笑照唇线点数组（可选）
   * @param mouthLipPoints 开口照唇线点数组（可选）
   * @param smileAlignPoints 微笑照对齐点数组（可选）
   * @param mouthAlignPoints 开口照对齐点数组（可选）
   */
  function setAllFaceData(
    points: FacePoint[],
    smileLipPoints?: { x: number; y: number }[],
    mouthLipPoints?: { x: number; y: number }[],
    smileAlignPoints?: { x: number; y: number }[],
    mouthAlignPoints?: { x: number; y: number }[]
  ) {
    // 设置面部关键点
    setSmileFaceLandmarks(points)

    // 设置微笑照唇线点（如果提供）
    if (smileLipPoints) {
      storeSmileLipPoints.value = smileLipPoints

      // 如果原始备份为空，则创建备份
      if (!storeOriginalSmileLipPoints.value || storeOriginalSmileLipPoints.value.length === 0) {
        storeOriginalSmileLipPoints.value = JSON.parse(JSON.stringify(smileLipPoints))
      }
    }

    // 设置开口照唇线点（如果提供）
    if (mouthLipPoints) {
      storeMouthLipPoints.value = mouthLipPoints
    }

    // 设置微笑照对齐点（如果提供）
    if (smileAlignPoints) {
      storeSmileAlignPoints.value = smileAlignPoints
      storeInitialSmileAlignPoints.value = smileAlignPoints.map((p) => ({ ...p }))
    }

    // 设置开口照对齐点（如果提供）
    if (mouthAlignPoints) {
      storeMouthAlignPoints.value = mouthAlignPoints
      storeInitialMouthAlignPoints.value = mouthAlignPoints.map((p) => ({ ...p }))
    }
  }

  /**
   * 更新单个面部关键点
   * @param index 关键点索引
   * @param point 新的关键点数据
   */
  function updateFacePoint(index: number, point: FacePoint) {
    if (index >= 0 && index < storeFaceLandmarks.value.length) {
      const newPoints = [...storeFaceLandmarks.value]
      newPoints[index] = point
      storeFaceLandmarks.value = newPoints
      storeFaceRotation.value = calcFaceRotation(newPoints)
    }
  }

  /**
   * 重置面部数据
   */
  function resetFaceData() {
    storeFaceLandmarks.value = []
    storeFaceRotation.value = 0
    storeSmileLipPoints.value = []
    storeMouthLipPoints.value = []
    storeOriginalSmileLipPoints.value = null
  }

  /**
   * 重置唇线点
   */
  function resetLipPoints() {
    if (storeOriginalSmileLipPoints.value) {
      storeSmileLipPoints.value = JSON.parse(JSON.stringify(storeOriginalSmileLipPoints.value))
    }
  }

  /**
   * 重置对齐点为初始点
   */
  function resetAlignPoints() {
    storeSmileAlignPoints.value = storeInitialSmileAlignPoints.value.map((p) => ({ ...p }))
    storeMouthAlignPoints.value = storeInitialMouthAlignPoints.value.map((p) => ({ ...p }))
  }

  /**
   * 清空对齐点
   */
  function clearAlignPoints() {
    storeSmileAlignPoints.value = []
    storeMouthAlignPoints.value = []
  }

  /**
   * 设置开口照的仿射变换参数（旋转、缩放、平移）
   * @param transform 变换参数
   */
  function setMouthToSmileTransform(transform: {
    angle: number
    scale: number
    offsetX: number
    offsetY: number
  }) {
    storeMouthToSmileTransform.value = { ...transform }
  }

  /**
   * 重置开口照的仿射变换参数
   */
  function resetMouthToSmileTransform() {
    storeMouthToSmileTransform.value = { angle: 0, scale: 1, offsetX: 0, offsetY: 0 }
  }

  // 计算属性：面部旋转角度
  const rotation = computed(() => storeFaceRotation.value)

  return {
    // 数据
    smileFaceLandmarks: storeFaceLandmarks, // 新的更清晰的名称
    rotation,
    smileLipPoints: storeSmileLipPoints,
    mouthLipPoints: storeMouthLipPoints,
    smileAlignPoints: storeSmileAlignPoints,
    mouthAlignPoints: storeMouthAlignPoints,
    mouthToSmileTransform: storeMouthToSmileTransform,

    // 方法
    setSmileFaceLandmarks,
    setAllFaceData,
    updateFacePoint,
    resetFaceData,
    resetLipPoints,
    resetAlignPoints,
    clearAlignPoints,
    setMouthToSmileTransform,
    resetMouthToSmileTransform
  }
}
