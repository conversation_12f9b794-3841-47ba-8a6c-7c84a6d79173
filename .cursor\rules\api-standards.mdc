---
description: 开发接口相关功能的时候
globs: 
alwaysApply: false
---
# API接口开发规范

本规范定义了项目中API接口的开发标准和最佳实践。

## 目录结构

- **API模块化**
  - API文件应放在 `src/api/` 目录下
  - 每个功能模块使用单独的文件，如 [auth.ts](mdc:src/api/auth.ts)
  - 常量和配置放在 [constants.ts](mdc:src/api/constants.ts)

- **类型定义**
  - 接口相关类型定义放在 `src/types/` 目录下
  - 按模块分类，如 [auth.ts](mdc:src/types/auth.ts)
  - 全局类型定义放在 [global.d.ts](mdc:src/types/global.d.ts)

## 请求工具

项目使用封装的 [request.ts](mdc:src/utils/request.ts) 作为统一的请求工具：

```typescript
// ✅ 正确的请求方法使用方式
import request from '@/utils/request'

export const someApi = {
  getData: () => request.get<ResponseType>('/endpoint'),
  createData: (data: RequestType) => request.post<ResponseType>('/endpoint', data)
}
```

## 类型规范

- **请求参数类型**
  ```typescript
  // ✅ 请求参数类型定义
  interface RequestParams {
    username: string
    password: string
    remember?: boolean
  }
  ```

- **响应数据类型**
  ```typescript
  // ✅ 响应数据类型定义
  interface ApiResponse<T> {
    code: number
    data: T
    message: string
  }
  ```

## API模块结构

- **模块化封装**
  ```typescript
  // ✅ 推荐的API模块结构
  export const moduleApi = {
    list: (params: ListParams) => request.get<ListResponse>('/module/list', { params }),
    detail: (id: string) => request.get<DetailResponse>(`/module/${id}`),
    create: (data: CreateParams) => request.post<CreateResponse>('/module', data),
    update: (id: string, data: UpdateParams) => request.put<UpdateResponse>(`/module/${id}`, data),
    delete: (id: string) => request.delete<void>(`/module/${id}`)
  }
  ```

## 错误处理

- **做了统一的错误处理机制**
  ```typescript
  // ✅ 在请求拦截器中统一处理错误
  service.interceptors.response.use(
    (response) => {
      if (response.data.code === 200) {
        return response
      }
      return Promise.reject(new Error(response.data.message))
    },
    (error) => {
      ElMessage.error(error.message)
      return Promise.reject(error)
    }
  )
  ```

## Mock数据

- **Mock文件结构**
  - Mock文件放在 `src/mocks/` 目录下
  - 数据定义放在 `data/` 子目录
  - 处理器放在 `handlers/` 子目录

```typescript
// ✅ Mock数据示例
export const mockData = {
  code: 200,
  data: {
    // 模拟数据
  },
  message: 'success'
}
```


## 最佳实践

1. **命名规范**
   - API函数使用动词+名词形式
   - 类型名称使用PascalCase
   - 请求参数类型以Params或Request结尾
   - 响应数据类型以Response结尾

2. **注释规范**
   ```typescript
   /**
    * 用户登录
    * @param {LoginParams} params - 登录参数
    * @returns {Promise<ApiResponse<LoginResult>>} 登录结果
    */
   login: (params: LoginParams) => request.post<LoginResult>('/auth/login', params)
   ```


## 开发流程

1. 在 `types/` 目录下定义接口类型
2. 在 `api/` 目录下创建对应模块的API文件
3. 在 `mocks/` 目录下创建对应的mock数据
4. 实现API调用和错误处理
5. 编写测试用例验证接口

## 测试规范

- 每个API模块都应该有对应的测试文件
- 测试应该覆盖成功和失败的场景
- 使用Mock服务拦截请求进行测试

