import { nextTick, Ref } from 'vue'
import { useTeethStore } from '@/store/teeth'
import { ElMessage } from 'element-plus'
import type { VeneerEditorContext } from '@/contexts/VeneerEditorContext'

/**
 * 重置微笑设计的组合式函数
 * @param canvasRef 画布组件引用
 * @param currentTemplate 当前模板
 * @param editorContext 贴面编辑器上下文
 */
export function useResetSmile(
  canvasRef: Ref<any>,
  currentTemplate: Ref<string>,
  editorContext: VeneerEditorContext
) {
  const teethStore = useTeethStore()

  async function handleReset() {
    // 1. 重置透明度 - 通过设置 sliderValue 为 0（完全显示开口照）
    editorContext.sliderValue.value = 0

    // 2. 重置用户交互标志，触发自动聚焦
    editorContext.resetInteraction()

    // 3. 提示
    ElMessage({
      message: '已重设微笑设计',
      type: 'success'
    })

    // 4. 等待 DOM 更新
    await nextTick()

    if (canvasRef.value) {
      // 5. 重置框架位置
      canvasRef.value.initFrameBasedOnLipPosition()

      // 6. 重置牙齿形状
      // 获取模板列表
      const templateList = teethStore.templates

      // 获取默认模板ID（索引0）
      const defaultTemplateId = templateList[0]?.id
      if (!defaultTemplateId) {
        console.error('未找到默认模板')
        return
      }

      // 重置所有自定义牙齿形状
      teethStore.resetAllToothShapes()

      // 设置为默认模板（索引0）
      teethStore.setCurrentTemplate(defaultTemplateId)
      currentTemplate.value = defaultTemplateId

      // 强制更新所有牙齿形状（无论当前是否已经是默认模板）
      teethStore.forceUpdateTeethShapes()

      // 7. 取消选中的牙齿
      teethStore.selectTooth(null)
    }
  }

  return { handleReset }
}
