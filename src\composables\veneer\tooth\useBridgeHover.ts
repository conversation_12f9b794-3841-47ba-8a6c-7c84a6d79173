import { ref } from 'vue'

// 全局连桥hover状态管理
const bridgeHoverState = ref<{
  isActive: boolean
  currentToothId: number | null
  adjacentToothId: number | null
}>({
  isActive: false,
  currentToothId: null,
  adjacentToothId: null
})

/**
 * 连桥hover状态管理 composable
 * 用于在连桥编辑时同时高亮显示两颗相邻的牙齿
 */
export function useBridgeHover() {
  /**
   * 设置连桥hover状态
   * @param currentToothId 当前牙齿ID
   * @param adjacentToothId 相邻牙齿ID
   */
  function setBridgeHover(currentToothId: number, adjacentToothId: number) {
    bridgeHoverState.value = {
      isActive: true,
      currentToothId,
      adjacentToothId
    }
  }

  /**
   * 清除连桥hover状态
   */
  function clearBridgeHover() {
    bridgeHoverState.value = {
      isActive: false,
      currentToothId: null,
      adjacentToothId: null
    }
  }

  /**
   * 检查指定牙齿是否处于连桥hover状态
   * @param toothId 牙齿ID
   * @returns 是否处于连桥hover状态
   */
  function isBridgeHovered(toothId: number): boolean {
    if (!bridgeHoverState.value.isActive) return false
    
    return (
      toothId === bridgeHoverState.value.currentToothId ||
      toothId === bridgeHoverState.value.adjacentToothId
    )
  }

  return {
    bridgeHoverState,
    setBridgeHover,
    clearBridgeHover,
    isBridgeHovered
  }
}
