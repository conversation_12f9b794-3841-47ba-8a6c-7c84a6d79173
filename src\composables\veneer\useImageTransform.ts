import { ref, nextTick } from 'vue'
import type { Ref } from 'vue'
import { useCommonStore } from '@/store/common'
import { storeToRefs } from 'pinia'
import { useImageTransformByWheel } from '@/composables/useImageTransformByWheel'
import { calcLipFocusScaleAndOffset as calcLipFocusScaleAndOffsetUtil } from '@/utils/transform2d'
import { TransformParams } from '@/utils/transform2d'
import { TARGET_RATIO } from './useCanvasStage'

/**
 * 处理图像变换（缩放、平移、旋转）
 */
export function useImageTransform(
  stageWidth: Ref<number>,
  stageHeight: Ref<number>,
  smileImgNaturalWidth: Ref<number>,
  smileImgNaturalHeight: Ref<number>
) {
  // 从store获取数据
  const commonStore = useCommonStore()
  const { faceRotation, smileLipPoints } = storeToRefs(commonStore)

  // 图片中心位置
  const imgCenter = ref({ x: 0, y: 0 })

  // 统一缩放/拖拽逻辑
  const {
    imgScale,
    offsetX,
    offsetY,
    handleWheel,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp
  } = useImageTransformByWheel({
    minScale: 0.05,
    maxScale: 3,
    scaleStep: 1.1,
    getImageSize: () => ({
      width: smileImgNaturalWidth.value,
      height: smileImgNaturalHeight.value
    }),
    onTransformUpdate: () => {
      imgCenter.value = {
        x: offsetX.value + (smileImgNaturalWidth.value * imgScale.value) / 2,
        y: offsetY.value + (smileImgNaturalHeight.value * imgScale.value) / 2
      }
    }
  })

  // 构造通用变换参数
  function getTransformParams(): TransformParams {
    return {
      scale: imgScale.value,
      offsetX: offsetX.value,
      offsetY: offsetY.value,
      rotation: faceRotation.value,
      anchorX: smileImgNaturalWidth.value / 2,
      anchorY: smileImgNaturalHeight.value / 2
    }
  }

  // 自动聚焦到牙齿区域
  function autoFocusToTeethArea() {
    if (!smileLipPoints.value || smileLipPoints.value.length < 2) {
      console.log('autoFocusToTeethArea: 唇线点不足，无法聚焦')
      return
    }

    // 使用工具函数计算唇部区域聚焦参数
    const result = calcLipFocusScaleAndOffsetUtil(
      smileLipPoints.value,
      stageWidth.value,
      stageHeight.value,
      {
        targetRatio: TARGET_RATIO, // 使用全局配置常量
        rotation: faceRotation.value,
        imgWidth: smileImgNaturalWidth.value,
        imgHeight: smileImgNaturalHeight.value,
        returnCenter: true
      }
    )

    // console.log('autoFocusToTeethArea: 计算聚焦参数', {
    //   scale: result.scale,
    //   center: result.center,
    //   stageWidth: stageWidth.value,
    //   stageHeight: stageHeight.value
    // })

    // 设置缩放和中心点
    imgScale.value = result.scale

    // 如果返回了中心点，使用它来计算偏移量
    if (result.center) {
      offsetX.value = result.center.x - (smileImgNaturalWidth.value * result.scale) / 2
      offsetY.value = result.center.y - (smileImgNaturalHeight.value * result.scale) / 2
    } else {
      // 使用默认偏移量
      offsetX.value = (stageWidth.value - smileImgNaturalWidth.value * result.scale) / 2
      offsetY.value = (stageHeight.value - smileImgNaturalHeight.value * result.scale) / 2
    }

    // 更新图片中心位置
    imgCenter.value = {
      x: offsetX.value + (smileImgNaturalWidth.value * imgScale.value) / 2,
      y: offsetY.value + (smileImgNaturalHeight.value * imgScale.value) / 2
    }

    // console.log('autoFocusToTeethArea: 更新图片变换参数', {
    //   scale: imgScale.value,
    //   offsetX: offsetX.value,
    //   offsetY: offsetY.value,
    //   centerX: imgCenter.value.x,
    //   centerY: imgCenter.value.y
    // })
  }

  // 重置交互状态
  function resetInteraction() {
    // 重置缩放和位置
    nextTick(() => {
      // 自动聚焦到牙齿区域
      autoFocusToTeethArea()
    })
  }

  return {
    imgScale,
    offsetX,
    offsetY,
    imgCenter,
    getTransformParams,
    autoFocusToTeethArea,
    handleWheel,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    resetInteraction
  }
}
