// 面部关键点类型定义
export type FacePointName =
  | 'leftEye'
  | 'rightEye'
  | 'leftNostril'
  | 'rightNostril'
  | 'leftMouth'
  | 'rightMouth'

export interface FacePoint {
  name: FacePointName
  x: number
  y: number
}

export interface FaceLandmark {
  points: FacePoint[]
  smileLipPoints?: { x: number; y: number }[]
  mouthLipPoints?: { x: number; y: number }[]
  lipPoints?: { x: number; y: number }[]
  smileAlignPoints?: { x: number; y: number }[]
  mouthAlignPoints?: { x: number; y: number }[]
}

// MediaPipe检测配置
export interface MediaPipeDetectionConfig {
  runtime: 'mediapipe' | 'tfjs'
  refineLandmarks?: boolean
  maxFaces?: number
  solutionPath?: string
}

// 检测结果
export interface DetectionResult {
  points: FacePoint[]
  smileLipPoints: { x: number; y: number }[]
  mouthLipPoints: { x: number; y: number }[]
  smileAlignPoints: { x: number; y: number }[]
  mouthAlignPoints: { x: number; y: number }[]
  confidence?: number
  totalKeypoints?: number
  processingTime?: number
  method?: 'mediapipe' | 'api'
}
