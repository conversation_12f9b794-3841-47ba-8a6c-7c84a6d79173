import { Ref, watch, onMounted } from 'vue'
import type { Point } from '@/utils/curveUtils'

/**
 * 处理牙齿初始化的 composable
 * 提供牙齿初始化和监听相关的逻辑
 *
 * @param controlPoints 牙齿控制点的响应式引用
 * @param isCustomShape 是否为自定义形状的响应式引用
 * @param relativeShape 相对形状的响应式引用
 * @param isRotating 是否正在旋转的响应式引用
 * @param edgeDragJustEnded 边缘拖拽是否刚刚结束的响应式引用
 * @param selected 是否被选中的响应式引用
 * @param isHovered 是否处于悬停状态的响应式引用
 * @param rotateIconObj 旋转图标对象的响应式引用
 * @param initializeControlPoints 初始化控制点的函数
 * @returns 初始化相关的方法
 */
export function useToothInit(
  controlPoints: Ref<Point[]>,
  isCustomShape: Ref<boolean>,
  relativeShape: Ref<Point[]>,
  isRotating: Ref<boolean>,
  edgeDragJustEnded: Ref<boolean>,
  selected: Ref<boolean>,
  isHovered: Ref<boolean>,
  rotateIconObj: Ref<HTMLImageElement | null>,
  initializeControlPoints: () => void
) {
  /**
   * 监听牙齿选中状态变化
   */
  watch(
    () => selected.value,
    (newSelected) => {
      // 当牙齿被选中或取消选中时，重置旋转状态
      isRotating.value = false

      // 如果牙齿被选中，重置悬停状态
      if (newSelected) {
        isHovered.value = false
      }
    }
  )

  /**
   * 初始化牙齿
   */
  function initializeTooth() {
    // 加载旋转图标
    const img = new Image()
    img.src = new URL('../../../assets/images/rotate.svg', import.meta.url).href
    img.onload = () => {
      rotateIconObj.value = img
    }

    // 初始化边缘拖拽标志
    edgeDragJustEnded.value = false

    // 初始化控制点
    initializeControlPoints()
  }

  /**
   * 监听牙齿形状变化
   * @param toothShape 牙齿形状对象
   */
  function setupShapeWatchers(toothShape: any) {
    // 监听控制点变化
    watch(
      () => toothShape.controlPoints.value,
      (newPoints) => {
        controlPoints.value = newPoints
      },
      { immediate: true, deep: true }
    )

    // 监听自定义形状标志变化
    watch(
      () => toothShape.isCustomShape.value,
      (newValue) => {
        isCustomShape.value = newValue
      },
      { immediate: true }
    )

    // 监听相对形状变化
    watch(
      () => toothShape.relativeShape.value,
      (newValue) => {
        relativeShape.value = newValue
      },
      { immediate: true, deep: true }
    )
  }

  // 在组件挂载时初始化牙齿
  onMounted(initializeTooth)

  return {
    initializeTooth,
    setupShapeWatchers
  }
}
