/**
 * MediaPipe Face Detection Web Worker
 * 在后台线程中执行面部检测，避免阻塞主线程
 */

// 导入 TensorFlow.js 和 MediaPipe
importScripts('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js');
importScripts('https://cdn.jsdelivr.net/npm/@tensorflow-models/face-landmarks-detection@1.0.5/dist/face-landmarks-detection.js');

let detector = null;
let isInitializing = false;

// MediaPipe关键点索引映射
const MEDIAPIPE_KEYPOINT_MAPPING = {
  leftEye: 468,
  rightEye: 473,
  leftNostril: 48,
  rightNostril: 278,
  leftMouth: 61,
  rightMouth: 291
};

// 内唇轮廓关键点索引
const LIP_KEYPOINTS = [
  78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308,
  415, 310, 311, 312, 13, 82, 81, 80
];

/**
 * 初始化MediaPipe检测器
 */
async function initializeDetector() {
  if (detector) return true;
  if (isInitializing) {
    while (isInitializing) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    return !!detector;
  }

  try {
    isInitializing = true;
    console.log('[Worker] 正在初始化MediaPipe Face Mesh检测器...');

    // 设置TensorFlow.js后端
    await tf.setBackend('webgl');
    await tf.ready();

    const model = faceLandmarksDetection.SupportedModels.MediaPipeFaceMesh;
    const detectorConfig = {
      runtime: 'mediapipe',
      solutionPath: 'https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh',
      refineLandmarks: true,
      maxFaces: 1
    };

    detector = await faceLandmarksDetection.createDetector(model, detectorConfig);
    console.log('[Worker] MediaPipe Face Mesh检测器初始化成功');
    return true;
  } catch (error) {
    console.error('[Worker] MediaPipe检测器初始化失败:', error);
    return false;
  } finally {
    isInitializing = false;
  }
}

/**
 * 从图片数据检测面部关键点
 */
async function detectFaceFromImageData(imageData, width, height) {
  try {
    // 确保检测器已初始化
    const initialized = await initializeDetector();
    if (!initialized || !detector) {
      throw new Error('检测器初始化失败');
    }

    console.log('[Worker] 开始检测面部关键点...');
    const startTime = performance.now();

    // 创建ImageData对象
    const imgData = new ImageData(new Uint8ClampedArray(imageData), width, height);
    
    // 执行面部检测
    const faces = await detector.estimateFaces(imgData, {
      flipHorizontal: false
    });

    const endTime = performance.now();
    console.log(`[Worker] 面部检测完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);

    if (faces.length === 0) {
      console.warn('[Worker] 未检测到面部');
      return null;
    }

    if (faces.length > 1) {
      console.warn(`[Worker] 检测到${faces.length}张面部，使用第一张`);
    }

    // 处理检测结果
    const result = processFaceDetectionResult(faces[0]);
    console.log('[Worker] 面部关键点处理完成');

    return result;
  } catch (error) {
    console.error('[Worker] 面部检测失败:', error);
    throw error;
  }
}

/**
 * 处理MediaPipe检测结果
 */
function processFaceDetectionResult(face) {
  const keypoints = face.keypoints;

  // 提取项目所需的6个关键点
  const facePoints = extractRequiredFacePoints(keypoints);

  // 提取唇线点
  const lipPoints = extractLipPoints(keypoints);

  // 提取对齐点
  const alignPoints = extractAlignPoints(keypoints);

  // 计算置信度
  const confidence = face.score || 0.9;

  return {
    points: facePoints,
    smileLipPoints: lipPoints,
    mouthLipPoints: lipPoints,
    smileAlignPoints: alignPoints,
    mouthAlignPoints: alignPoints,
    confidence,
    totalKeypoints: keypoints.length
  };
}

/**
 * 提取项目所需的6个关键点
 */
function extractRequiredFacePoints(keypoints) {
  const facePoints = [];

  Object.entries(MEDIAPIPE_KEYPOINT_MAPPING).forEach(([name, index]) => {
    if (index < keypoints.length) {
      const point = keypoints[index];
      facePoints.push({
        name: name,
        x: point.x,
        y: point.y
      });
    }
  });

  return facePoints;
}

/**
 * 提取唇线点
 */
function extractLipPoints(keypoints) {
  const lipPoints = [];

  LIP_KEYPOINTS.forEach((index) => {
    if (index < keypoints.length) {
      const point = keypoints[index];
      lipPoints.push({
        x: point.x,
        y: point.y
      });
    }
  });

  return lipPoints;
}

/**
 * 提取对齐点
 */
function extractAlignPoints(keypoints) {
  const alignPoints = [];

  // 使用上下唇部的特定点位来计算对齐点
  const upperLipLeft = 82;
  const upperLipRight = 312;
  const lowerLipLeft = 87;
  const lowerLipRight = 317;

  // 检查所有需要的点位是否存在
  if (upperLipLeft < keypoints.length && 
      upperLipRight < keypoints.length && 
      lowerLipLeft < keypoints.length && 
      lowerLipRight < keypoints.length) {
    
    // 获取四个关键点的坐标
    const upperLeft = keypoints[upperLipLeft];
    const upperRight = keypoints[upperLipRight];
    const lowerLeft = keypoints[lowerLipLeft];
    const lowerRight = keypoints[lowerLipRight];

    // 计算第一个对齐点：82和87连线的中点（左侧）
    const leftAlignPoint = {
      x: (upperLeft.x + lowerLeft.x) / 2,
      y: (upperLeft.y + lowerLeft.y) / 2
    };

    // 计算第二个对齐点：312和317连线的中点（右侧）
    const rightAlignPoint = {
      x: (upperRight.x + lowerRight.x) / 2,
      y: (upperRight.y + lowerRight.y) / 2
    };

    alignPoints.push(leftAlignPoint, rightAlignPoint);
  } else {
    console.warn('[Worker] 无法获取对齐点所需的唇部关键点，使用备用方案');
    
    // 备用方案：使用嘴角点
    const leftMouthCorner = 61;
    const rightMouthCorner = 291;
    
    const backupIndices = [leftMouthCorner, rightMouthCorner];
    backupIndices.forEach((index) => {
      if (index < keypoints.length) {
        const point = keypoints[index];
        alignPoints.push({
          x: point.x,
          y: point.y
        });
      }
    });
  }

  return alignPoints;
}

// 监听主线程消息
self.onmessage = async function(e) {
  const { type, data, id } = e.data;

  try {
    switch (type) {
      case 'INIT':
        const initSuccess = await initializeDetector();
        self.postMessage({
          type: 'INIT_RESULT',
          data: { success: initSuccess },
          id
        });
        break;

      case 'DETECT':
        const { imageData, width, height, label } = data;
        const result = await detectFaceFromImageData(imageData, width, height);
        self.postMessage({
          type: 'DETECT_RESULT',
          data: { result, label },
          id
        });
        break;

      case 'DISPOSE':
        if (detector) {
          detector.dispose();
          detector = null;
          console.log('[Worker] MediaPipe检测器已销毁');
        }
        self.postMessage({
          type: 'DISPOSE_RESULT',
          data: { success: true },
          id
        });
        break;

      default:
        throw new Error(`未知的消息类型: ${type}`);
    }
  } catch (error) {
    console.error('[Worker] 处理消息失败:', error);
    self.postMessage({
      type: 'ERROR',
      data: { error: error.message },
      id
    });
  }
};

console.log('[Worker] MediaPipe Worker 已启动');
