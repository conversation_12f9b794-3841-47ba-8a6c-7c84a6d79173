import { ref, computed } from 'vue'
import { useCommonStore } from '@/store/common'
import { storeToRefs } from 'pinia'

/**
 * 图片上传相关功能的组合式函数
 * 提供图片上传、清除和状态管理功能
 */
export function useImageUpload() {
  // 从store获取图片数据
  const commonStore = useCommonStore()
  const { smileImage: storeSmileImage, mouthImage: storeMouthImage } = storeToRefs(commonStore)

  // 创建计算属性，确保返回的是字符串类型，与 ImageUploader 组件兼容
  const smileImage = computed({
    get: () => storeSmileImage.value || '',
    set: (value: string) => {
      storeSmileImage.value = value
    }
  })

  const mouthImage = computed({
    get: () => storeMouthImage.value || '',
    set: (value: string) => {
      storeMouthImage.value = value
    }
  })

  // 本地状态
  const uploadStatus = ref({
    smile: false,
    mouth: false
  })

  // 初始化上传状态
  if (storeSmileImage.value) {
    uploadStatus.value.smile = true
  }
  if (storeMouthImage.value) {
    uploadStatus.value.mouth = true
  }

  /**
   * 设置图片
   * @param type 图片类型 ('smile' | 'mouth')
   * @param imageData 图片数据
   */
  function setImage(type: 'smile' | 'mouth', imageData: string) {
    if (type === 'smile') {
      smileImage.value = imageData
      uploadStatus.value.smile = true
    } else if (type === 'mouth') {
      mouthImage.value = imageData
      uploadStatus.value.mouth = true
    }
  }

  /**
   * 清除所有图片
   */
  function clearImages() {
    smileImage.value = ''
    mouthImage.value = ''
    uploadStatus.value.smile = false
    uploadStatus.value.mouth = false
  }

  /**
   * 清除指定类型的图片
   * @param type 图片类型 ('smile' | 'mouth')
   */
  function clearImage(type: 'smile' | 'mouth') {
    if (type === 'smile') {
      smileImage.value = ''
      uploadStatus.value.smile = false
    } else if (type === 'mouth') {
      mouthImage.value = ''
      uploadStatus.value.mouth = false
    }
  }

  /**
   * 是否可以进入下一步
   */
  const canProceed = () => uploadStatus.value.smile && uploadStatus.value.mouth

  return {
    uploadStatus,
    setImage,
    clearImages,
    clearImage,
    canProceed,
    smileImage,
    mouthImage
  }
}
