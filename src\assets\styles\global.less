@import './variables.less';

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  color: @text-color;
  line-height: 1.6;
  font-size: 16px;
}

// 通用布局类
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 @spacing-base;
}

// 通用间距类
.mt-small { margin-top: @spacing-small; }
.mt-base { margin-top: @spacing-base; }
.mt-large { margin-top: @spacing-large; }

.mb-small { margin-bottom: @spacing-small; }
.mb-base { margin-bottom: @spacing-base; }
.mb-large { margin-bottom: @spacing-large; }

// 通用文本类
.text-primary {
  color: @primary-color;
}

.text-secondary {
  color: @text-color-secondary;
}

// 通用卡片样式
.card {
  background: #fff;
  border-radius: @border-radius-base;
  padding: @spacing-base;
  box-shadow: @box-shadow-base;
}

// 通用按钮样式
.btn {
  display: inline-block;
  padding: @spacing-small @spacing-base;
  border-radius: @border-radius-base;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  outline: none;
  
  &.btn-primary {
    background: @primary-color;
    color: #fff;
    
    &:hover {
      opacity: 0.9;
    }
  }
  
  &.btn-secondary {
    background: @secondary-color;
    color: #fff;
    
    &:hover {
      opacity: 0.9;
    }
  }
} 