<template>
  <div class="control-panel">
    <div class="panel-title">微笑控制</div>

    <div class="panel-section">
      <div class="section-label">切换前后照片</div>
      <div class="slider-container">
        <el-slider
          v-model="sliderValue"
          :min="0"
          :max="1"
          :step="0.01"
          :format-tooltip="(val: number) => Math.round(val * 100) + '%'"
          :show-tooltip="false"
        />
        <div class="slider-value">{{ Math.round(sliderValue * 100) }}%</div>
      </div>
    </div>

    <div class="panel-section checkbox-section">
      <div class="checkbox-item">
        <div class="section-label">微笑框线</div>
        <el-checkbox v-model="showSmileFrame" checked class="custom-checkbox"></el-checkbox>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'

// Props
const props = defineProps({
  sliderValue: {
    type: Number,
    required: true
  },
  showSmileFrame: {
    type: Boolean,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:sliderValue', 'update:showSmileFrame'])

// 本地滑块值，用于防抖处理
const localSliderValue = ref(props.sliderValue)

// 计算属性，用于双向绑定
const sliderValue = computed({
  get: () => localSliderValue.value,
  set: (value) => {
    localSliderValue.value = value
    // 使用requestAnimationFrame进行防抖处理，减少更新频率
    requestAnimationFrame(() => {
      emit('update:sliderValue', value)
    })
  }
})

const showSmileFrame = computed({
  get: () => props.showSmileFrame,
  set: (value) => emit('update:showSmileFrame', value)
})

// 监听props变化，同步本地值
watch(
  () => props.sliderValue,
  (newValue) => {
    // 无条件更新本地值，确保外部变化能够正确反映
    localSliderValue.value = newValue
  },
  { immediate: true } // 立即执行一次，确保初始值同步
)
</script>

<style scoped>
.control-panel {
  background: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 12px 15px;
  border-radius: 8px;
  margin-bottom: 10px;
  background-color: rgba(255, 245, 235, 0.9);
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.panel-section {
  margin-bottom: 12px;
}

.section-label {
  font-size: 15px;
  color: #333;
  font-weight: normal;
}

/* 滑块样式 */
.slider-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.slider-label {
  font-size: 14px;
  color: #0052d9;
  width: 45px;
}

.slider-value {
  font-size: 14px;
  color: #0052d9;
  width: 45px;
  text-align: right;
  font-weight: 500;
}

/* 复选框样式 */
.checkbox-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-bottom: 0;
}

.checkbox-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-checkbox) {
  height: 20px;
}

:deep(.el-slider__runway) {
  margin: 0;
  flex: 1;
}

:deep(.el-slider__bar) {
  border-radius: 3px;
  height: 6px;
  background-color: transparent;
}

:deep(.el-slider__runway) {
  height: 6px;
  background: linear-gradient(to right, #797878, #e6e6e6);
}

:deep(.el-slider__button) {
  border-color: #0052d9;
  box-shadow: 0 0 2px rgba(0, 82, 217, 0.5);
  width: 14px;
  height: 14px;
}
</style>
