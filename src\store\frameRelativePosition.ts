import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

/**
 * 框架相对位置状态接口
 * 存储框架相对于图片的位置关系
 */
export interface FrameRelativePositionState {
  // 框架中心点相对于图片中心点的偏移比例（相对于图片尺寸的比例，范围通常在 -1 到 1 之间）
  offsetRatioX: number
  offsetRatioY: number

  // 框架宽度相对于图片宽度的比例
  widthRatio: number

  // 框架高度相对于图片高度的比例
  heightRatio: number

  // 是否已经初始化过相对位置
  initialized: boolean
}

/**
 * 默认的框架相对位置状态
 */
const defaultState: FrameRelativePositionState = {
  offsetRatioX: 0, // 默认框架中心与图片中心在X轴对齐
  offsetRatioY: 0, // 默认框架中心与图片中心在Y轴对齐
  widthRatio: 0.6, // 默认框架宽度为图片宽度的60%
  heightRatio: 0.15, // 默认框架高度为图片高度的15%
  initialized: false // 初始状态为未初始化
}

/**
 * 框架相对位置状态管理Store
 * 用于在贴面设计页面和纹理设计页面之间同步框架相对于图片的位置关系
 */
export const useFrameRelativePositionStore = defineStore('frameRelativePosition', () => {
  // 状态
  const offsetRatioX = ref(defaultState.offsetRatioX)
  const offsetRatioY = ref(defaultState.offsetRatioY)
  const widthRatio = ref(defaultState.widthRatio)
  const heightRatio = ref(defaultState.heightRatio)
  const initialized = ref(defaultState.initialized)

  // 曲线控制点相对比例
  const topCurveLeftYRatio = ref(0)
  const topCurveMidYRatio = ref(0.1) // 默认下沉为框架高度的10%
  const topCurveRightYRatio = ref(0)
  const bottomCurveLeftYRatio = ref(1)
  const bottomCurveMidYRatio = ref(1.2) // 默认下沉为框架高度的20%
  const bottomCurveRightYRatio = ref(1)

  // 计算属性
  const state = computed(() => ({
    offsetRatioX: offsetRatioX.value,
    offsetRatioY: offsetRatioY.value,
    widthRatio: widthRatio.value,
    heightRatio: heightRatio.value,
    initialized: initialized.value
  }))

  /**
   * 更新框架相对位置
   * @param newState 新的相对位置状态
   */
  function updateRelativePosition(newState: Partial<FrameRelativePositionState>) {
    if (newState.offsetRatioX !== undefined) offsetRatioX.value = newState.offsetRatioX
    if (newState.offsetRatioY !== undefined) offsetRatioY.value = newState.offsetRatioY
    if (newState.widthRatio !== undefined) widthRatio.value = newState.widthRatio
    if (newState.heightRatio !== undefined) heightRatio.value = newState.heightRatio
    if (newState.initialized !== undefined) initialized.value = newState.initialized
  }

  /**
   * 计算框架相对位置
   * @param frameX 框架X坐标
   * @param frameY 框架Y坐标
   * @param frameWidth 框架宽度
   * @param frameHeight 框架高度
   * @param imgCenterX 图片中心X坐标
   * @param imgCenterY 图片中心Y坐标
   * @param imgWidth 图片宽度（缩放后）
   * @param imgHeight 图片高度（缩放后）
   * @param topCurvePoints 上曲线控制点
   * @param bottomCurvePoints 下曲线控制点
   */
  function calculateRelativePosition(
    frameX: number,
    frameY: number,
    frameWidth: number,
    frameHeight: number,
    imgCenterX: number,
    imgCenterY: number,
    imgWidth: number,
    imgHeight: number,
    topCurvePoints?: { leftY: number; midY: number; rightY: number },
    bottomCurvePoints?: { leftY: number; midY: number; rightY: number }
  ) {
    // 计算框架中心点
    const frameCenterX = frameX + frameWidth / 2
    const frameCenterY = frameY + frameHeight / 2

    // 计算框架中心点相对于图片中心点的偏移（像素）
    const offsetX = frameCenterX - imgCenterX
    const offsetY = frameCenterY - imgCenterY

    // 计算偏移比例（相对于图片尺寸的一半）
    const newOffsetRatioX = offsetX / (imgWidth / 2)
    const newOffsetRatioY = offsetY / (imgHeight / 2)

    // 计算宽高比例
    const newWidthRatio = frameWidth / imgWidth
    const newHeightRatio = frameHeight / imgHeight

    // 保存曲线控制点数据
    const topCurveData = topCurvePoints
      ? {
          leftYRatio: topCurvePoints.leftY / frameHeight,
          midYRatio: topCurvePoints.midY / frameHeight,
          rightYRatio: topCurvePoints.rightY / frameHeight
        }
      : null

    const bottomCurveData = bottomCurvePoints
      ? {
          leftYRatio: bottomCurvePoints.leftY / frameHeight,
          midYRatio: bottomCurvePoints.midY / frameHeight,
          rightYRatio: bottomCurvePoints.rightY / frameHeight
        }
      : null

    // 更新状态
    updateRelativePosition({
      offsetRatioX: newOffsetRatioX,
      offsetRatioY: newOffsetRatioY,
      widthRatio: newWidthRatio,
      heightRatio: newHeightRatio,
      initialized: true
    })

    // 保存曲线控制点数据
    if (topCurveData) {
      topCurveLeftYRatio.value = topCurveData.leftYRatio
      topCurveMidYRatio.value = topCurveData.midYRatio
      topCurveRightYRatio.value = topCurveData.rightYRatio
    }

    if (bottomCurveData) {
      bottomCurveLeftYRatio.value = bottomCurveData.leftYRatio
      bottomCurveMidYRatio.value = bottomCurveData.midYRatio
      bottomCurveRightYRatio.value = bottomCurveData.rightYRatio
    }
  }

  /**
   * 根据相对位置和当前图片状态计算框架的绝对位置
   * @param imgCenterX 图片中心X坐标
   * @param imgCenterY 图片中心Y坐标
   * @param imgWidth 图片宽度（缩放后）
   * @param imgHeight 图片高度（缩放后）
   * @returns 框架的绝对位置和尺寸
   */
  function calculateAbsolutePosition(
    imgCenterX: number,
    imgCenterY: number,
    imgWidth: number,
    imgHeight: number
  ) {
    // 计算框架中心点相对于图片中心点的偏移（像素）
    const offsetX = (offsetRatioX.value * imgWidth) / 2
    const offsetY = (offsetRatioY.value * imgHeight) / 2

    // 计算框架中心点
    const frameCenterX = imgCenterX + offsetX
    const frameCenterY = imgCenterY + offsetY

    // 计算框架尺寸
    const frameWidth = widthRatio.value * imgWidth
    const frameHeight = heightRatio.value * imgHeight

    // 计算框架左上角坐标
    const frameX = frameCenterX - frameWidth / 2
    const frameY = frameCenterY - frameHeight / 2

    return {
      x: frameX,
      y: frameY,
      width: frameWidth,
      height: frameHeight,
      // 使用保存的曲线控制点数据
      topCurvePoints: {
        leftY: topCurveLeftYRatio.value * frameHeight,
        midY: topCurveMidYRatio.value * frameHeight,
        rightY: topCurveRightYRatio.value * frameHeight
      },
      bottomCurvePoints: {
        leftY: bottomCurveLeftYRatio.value * frameHeight,
        midY: bottomCurveMidYRatio.value * frameHeight,
        rightY: bottomCurveRightYRatio.value * frameHeight
      }
    }
  }

  /**
   * 重置框架相对位置到默认状态
   */
  function resetRelativePosition() {
    updateRelativePosition({
      offsetRatioX: defaultState.offsetRatioX,
      offsetRatioY: defaultState.offsetRatioY,
      widthRatio: defaultState.widthRatio,
      heightRatio: defaultState.heightRatio,
      initialized: false
    })

    // 重置曲线控制点数据
    topCurveLeftYRatio.value = 0
    topCurveMidYRatio.value = 0.1
    topCurveRightYRatio.value = 0
    bottomCurveLeftYRatio.value = 1
    bottomCurveMidYRatio.value = 1.2
    bottomCurveRightYRatio.value = 1
  }

  return {
    // 状态
    offsetRatioX,
    offsetRatioY,
    widthRatio,
    heightRatio,
    initialized,
    state,

    // 方法
    updateRelativePosition,
    calculateRelativePosition,
    calculateAbsolutePosition,
    resetRelativePosition
  }
})
