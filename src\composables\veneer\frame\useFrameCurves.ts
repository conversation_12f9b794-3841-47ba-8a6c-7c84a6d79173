import { computed, ComputedRef, Ref } from 'vue'
import { useTeethStore } from '@/store/teeth'
import type { TeethFrame } from '@/store/teeth'
import type { KonvaEventObject } from 'konva/lib/Node'

/**
 * 框架曲线管理的组合式函数
 * 处理框架上下曲线的计算和更新
 */
export function useFrameCurves(frame: ComputedRef<TeethFrame>, dragUpdateScheduled: Ref<boolean>) {
  const teethStore = useTeethStore()

  // 常量定义
  const tensionFactor = 0.35

  // 曲线端点和中点的Y坐标
  const topCurveEndpointY = computed(() => frame.value.topCurvePoints.leftY + frame.value.y)
  const topCurveMidY = computed(() => frame.value.topCurvePoints.midY + frame.value.y)
  const bottomCurveEndpointY = computed(() => frame.value.bottomCurvePoints.leftY + frame.value.y)
  const bottomCurveMidY = computed(() => frame.value.bottomCurvePoints.midY + frame.value.y)

  // 宽度控制锚点的Y坐标 (位于上下曲线端点Y坐标的中间)
  const widthAnchorsY = computed(() => (topCurveEndpointY.value + bottomCurveEndpointY.value) / 2)

  // 主控制锚点的Y坐标 (位于上曲线中点上方固定距离)
  const MASTER_ANCHOR_OFFSET_Y = -40
  const masterAnchorY = computed(() => topCurveMidY.value + MASTER_ANCHOR_OFFSET_Y)

  // 框架位置和尺寸
  const currentMidX = computed(() => frame.value.x + frame.value.width / 2)
  const currentStartX = computed(() => frame.value.x)
  const currentEndX = computed(() => frame.value.x + frame.value.width)

  // 计算上曲线的点
  const topCurvePoints = computed(() => {
    return [
      currentStartX.value,
      topCurveEndpointY.value,
      currentMidX.value,
      topCurveMidY.value,
      currentEndX.value,
      topCurveEndpointY.value
    ]
  })

  // 计算下曲线的点
  const bottomCurvePoints = computed(() => {
    return [
      currentStartX.value,
      bottomCurveEndpointY.value,
      currentMidX.value,
      bottomCurveMidY.value,
      currentEndX.value,
      bottomCurveEndpointY.value
    ]
  })

  // 处理上曲线左端点拖动
  function handleLeftAnchor1Drag(e: KonvaEventObject<DragEvent>) {
    const newY = e.target.y()
    if (isNaN(newY)) return

    // 保持左右对称
    if (!dragUpdateScheduled.value) {
      dragUpdateScheduled.value = true
      requestAnimationFrame(() => {
        teethStore.updateFrame({
          topCurvePoints: {
            ...frame.value.topCurvePoints,
            leftY: newY - frame.value.y,
            rightY: newY - frame.value.y
          }
        })
        dragUpdateScheduled.value = false
      })
    }
  }

  // 处理上曲线右端点拖动
  function handleRightAnchor1Drag(e: KonvaEventObject<DragEvent>) {
    const newY = e.target.y()
    if (isNaN(newY)) return

    // 保持左右对称
    if (!dragUpdateScheduled.value) {
      dragUpdateScheduled.value = true
      requestAnimationFrame(() => {
        teethStore.updateFrame({
          topCurvePoints: {
            ...frame.value.topCurvePoints,
            leftY: newY - frame.value.y,
            rightY: newY - frame.value.y
          }
        })
        dragUpdateScheduled.value = false
      })
    }
  }

  // 处理下曲线左端点拖动
  function handleLeftAnchor2Drag(e: KonvaEventObject<DragEvent>) {
    const newY = e.target.y()
    if (isNaN(newY)) return

    // 保持左右对称
    if (!dragUpdateScheduled.value) {
      dragUpdateScheduled.value = true
      requestAnimationFrame(() => {
        teethStore.updateFrame({
          bottomCurvePoints: {
            ...frame.value.bottomCurvePoints,
            leftY: newY - frame.value.y,
            rightY: newY - frame.value.y
          }
        })
        dragUpdateScheduled.value = false
      })
    }
  }

  // 处理下曲线右端点拖动
  function handleRightAnchor2Drag(e: KonvaEventObject<DragEvent>) {
    const newY = e.target.y()
    if (isNaN(newY)) return

    // 保持左右对称
    if (!dragUpdateScheduled.value) {
      dragUpdateScheduled.value = true
      requestAnimationFrame(() => {
        teethStore.updateFrame({
          bottomCurvePoints: {
            ...frame.value.bottomCurvePoints,
            leftY: newY - frame.value.y,
            rightY: newY - frame.value.y
          }
        })
        dragUpdateScheduled.value = false
      })
    }
  }

  return {
    // 常量
    tensionFactor,

    // 计算属性
    currentMidX,
    currentStartX,
    currentEndX,
    topCurveEndpointY,
    topCurveMidY,
    bottomCurveEndpointY,
    bottomCurveMidY,
    widthAnchorsY,
    masterAnchorY,
    topCurvePoints,
    bottomCurvePoints,

    // 方法
    handleLeftAnchor1Drag,
    handleRightAnchor1Drag,
    handleLeftAnchor2Drag,
    handleRightAnchor2Drag
  }
}
