---
description: 
globs: 
alwaysApply: true
---
# Smile Design 项目总览

## 技术栈

- **核心框架**
  - Vue 3：使用 Composition API 和 setup 语法糖
  - TypeScript：类型系统
  - Vite：构建工具

- **状态管理 & 路由**
  - Pinia：状态管理方案
  - Vue Router：路由管理

- **UI & 样式**
  - Element Plus：UI组件库
  - Less：CSS预处理器
  - Konva.js：2D图形编辑库

- **工具库**
  - Vue I18n：国际化解决方案
  - Axios：HTTP请求库


## 开发规范

### 1. 文件命名规范

- **Vue组件**
  ```
  ✅ UserProfile.vue    // 组件使用 PascalCase
  ✅ LoginForm.vue
  ❌ user-profile.vue
  ```

- **TypeScript文件**
  ```
  ✅ userService.ts     // 服务类文件使用 camelCase
  ✅ types.ts
  ❌ UserService.ts
  ```

### 2. 代码规范参考

- Vue + TypeScript 开发规范：[vue-typescript.mdc](mdc:.cursor/rules/vue-typescript.mdc)
- API 接口规范：[api-standards.mdc](mdc:.cursor/rules/api-standards.mdc)

## Git 工作流

1. **分支命名**
   - 功能分支：`feature/功能名称`
   - 修复分支：`fix/问题描述`
   - 优化分支：`optimize/优化描述`

2. **提交信息**
   ```
   feat(模块): 添加了新功能
   fix(模块): 修复了某个问题
   docs(模块): 更新了文档
   style(模块): 代码格式调整
   refactor(模块): 代码重构
   test(模块): 添加测试
   chore(模块): 构建过程或辅助工具的变动
   ```

## 开发流程

1. 任务分配和规划
2. 功能开发
3. 代码审查
4. 测试验证
5. 部署上线


## 环境配置

- **.env 文件**
  ```
  # .env.development
  VITE_API_BASE_URL=/api
  VITE_APP_TITLE=Smile Design (Dev)

  # .env.production
  VITE_API_BASE_URL=https://api.example.com
  VITE_APP_TITLE=Smile Design
  ```

## 项目文档

更多详细信息请参考：
- [docs/what-is.md](mdc:docs/what-is.md) - 项目介绍
- [api-standards.mdc](mdc:.cursor/rules/api-standards.mdc) - API开发规范
- [vue-typescript.mdc](mdc:.cursor/rules/vue-typescript.mdc) - Vue + TypeScript 开发规范



