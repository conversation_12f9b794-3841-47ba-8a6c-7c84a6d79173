<template>
  <div class="worker-test">
    <h2>MediaPipe Worker 测试</h2>
    
    <div class="test-controls">
      <el-button @click="testWorker" :loading="isLoading" type="primary">
        测试Worker检测
      </el-button>
      <el-button @click="testMainThread" :loading="isLoading" type="success">
        测试主线程检测
      </el-button>
      <el-button @click="clearResults" type="warning">
        清除结果
      </el-button>
    </div>

    <div class="test-results">
      <div v-if="workerResult" class="result-section">
        <h3>Worker检测结果</h3>
        <p>检测时间: {{ workerTime }}ms</p>
        <p>面部关键点: {{ workerResult.points?.length || 0 }}个</p>
        <p>唇线点: {{ workerResult.smileLipPoints?.length || 0 }}个</p>
        <p>对齐点: {{ workerResult.smileAlignPoints?.length || 0 }}个</p>
        <p>置信度: {{ workerResult.confidence }}</p>
      </div>

      <div v-if="mainThreadResult" class="result-section">
        <h3>主线程检测结果</h3>
        <p>检测时间: {{ mainThreadTime }}ms</p>
        <p>面部关键点: {{ mainThreadResult.points?.length || 0 }}个</p>
        <p>唇线点: {{ mainThreadResult.smileLipPoints?.length || 0 }}个</p>
        <p>对齐点: {{ mainThreadResult.smileAlignPoints?.length || 0 }}个</p>
        <p>置信度: {{ mainThreadResult.confidence }}</p>
      </div>

      <div v-if="errorMessage" class="error-section">
        <h3>错误信息</h3>
        <p>{{ errorMessage }}</p>
      </div>
    </div>

    <div class="performance-comparison" v-if="workerTime && mainThreadTime">
      <h3>性能对比</h3>
      <p>Worker检测: {{ workerTime }}ms</p>
      <p>主线程检测: {{ mainThreadTime }}ms</p>
      <p>性能提升: {{ ((mainThreadTime - workerTime) / mainThreadTime * 100).toFixed(1) }}%</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { useAIDetection } from '@/composables/useAIDetection'
import { useCommonStore } from '@/store/common'

const isLoading = ref(false)
const workerResult = ref<any>(null)
const mainThreadResult = ref<any>(null)
const workerTime = ref(0)
const mainThreadTime = ref(0)
const errorMessage = ref('')

const aiDetection = useAIDetection()
const commonStore = useCommonStore()

/**
 * 测试Worker检测
 */
async function testWorker() {
  if (!commonStore.smileImage) {
    ElMessage.error('请先上传微笑照片')
    return
  }

  try {
    isLoading.value = true
    errorMessage.value = ''
    
    console.log('开始Worker检测测试...')
    const startTime = performance.now()
    
    // 使用Worker检测
    const result = await aiDetection.detectSingleImage(commonStore.smileImage, '微笑照(Worker)')
    
    const endTime = performance.now()
    workerTime.value = Math.round(endTime - startTime)
    workerResult.value = result
    
    if (result) {
      ElMessage.success(`Worker检测成功！耗时: ${workerTime.value}ms`)
    } else {
      ElMessage.warning('Worker未检测到面部')
    }
  } catch (error) {
    console.error('Worker检测失败:', error)
    errorMessage.value = `Worker检测失败: ${error}`
    ElMessage.error('Worker检测失败')
  } finally {
    isLoading.value = false
  }
}

/**
 * 测试主线程检测
 */
async function testMainThread() {
  if (!commonStore.smileImage) {
    ElMessage.error('请先上传微笑照片')
    return
  }

  try {
    isLoading.value = true
    errorMessage.value = ''
    
    console.log('开始主线程检测测试...')
    const startTime = performance.now()
    
    // 创建图片元素
    const img = new Image()
    img.crossOrigin = 'anonymous'
    
    await new Promise<void>((resolve, reject) => {
      img.onload = () => resolve()
      img.onerror = () => reject(new Error('图片加载失败'))
      img.src = commonStore.smileImage!
    })
    
    // 使用主线程检测
    const result = await aiDetection.detectFromImage(img)
    
    const endTime = performance.now()
    mainThreadTime.value = Math.round(endTime - startTime)
    
    if (result) {
      // 获取检测结果
      mainThreadResult.value = {
        points: aiDetection.smileFaceLandmarks.value,
        smileLipPoints: aiDetection.smileLipPoints.value,
        smileAlignPoints: aiDetection.smileAlignPoints.value,
        confidence: 0.9
      }
      ElMessage.success(`主线程检测成功！耗时: ${mainThreadTime.value}ms`)
    } else {
      ElMessage.warning('主线程未检测到面部')
    }
  } catch (error) {
    console.error('主线程检测失败:', error)
    errorMessage.value = `主线程检测失败: ${error}`
    ElMessage.error('主线程检测失败')
  } finally {
    isLoading.value = false
  }
}

/**
 * 清除结果
 */
function clearResults() {
  workerResult.value = null
  mainThreadResult.value = null
  workerTime.value = 0
  mainThreadTime.value = 0
  errorMessage.value = ''
}
</script>

<style scoped>
.worker-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-controls {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.test-results {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.result-section,
.error-section {
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.result-section h3,
.error-section h3 {
  margin-top: 0;
  color: #333;
}

.error-section {
  grid-column: 1 / -1;
  background: #fef0f0;
  border-color: #fbc4c4;
}

.performance-comparison {
  padding: 15px;
  border: 1px solid #67c23a;
  border-radius: 8px;
  background: #f0f9ff;
}

.performance-comparison h3 {
  margin-top: 0;
  color: #67c23a;
}
</style>
