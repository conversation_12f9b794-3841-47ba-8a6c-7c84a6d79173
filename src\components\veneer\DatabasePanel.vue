<template>
  <div class="control-panel database-panel">
    <div class="panel-title">微笑数据库</div>
    <div class="template-list">
      <div
        v-for="template in templates"
        :key="template"
        :class="['template-item', currentTemplate === template ? 'active' : '']"
        @click="selectTemplate(template)"
      >
        {{ template }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Props
const props = defineProps({
  templates: {
    type: Array as () => string[],
    required: true
  },
  currentTemplate: {
    type: String,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:currentTemplate'])

// 选择模板
function selectTemplate(template: string) {
  emit('update:currentTemplate', template)
}
</script>

<style scoped>
.database-panel {
  padding-top: 12px;
  padding-bottom: 12px;
  background: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 12px 15px;
  border-radius: 8px;
  margin-bottom: 10px;
  background-color: rgba(255, 245, 235, 0.9);
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.template-list {
  display: flex;
  flex-direction: column;
  gap: 0;
  overflow-y: auto;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.8);
  scrollbar-width: none;
}

.template-item {
  padding: 4px 10px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
  color: #333;
}

.template-item:last-child {
  border-bottom: none;
}

.template-item:hover {
  background-color: #f5f7fa;
}

.template-item.active {
  color: #4169e1;
  font-weight: bold;
  background-color: rgba(65, 105, 225, 0.1);
}
</style>
