# Task ID: 6
# Title: 贴面应用与调整功能
# Status: done
# Dependencies: 5, 11
# Priority: medium
# Description: 实现贴面（牙齿贴片）在微笑设计中的应用与多维度调整，支持多套牙齿曲线库、整体与单颗牙齿的灵活编辑、框架控制、镜像与连锁操作等，满足精细化美学设计需求。
# Details:
1. 照片加载与对齐
   - 页面初始时加载微笑照和开口照，并利用前置步骤中对齐操作获得的参数，使两张照片在画布中对齐、居中、聚焦显示。
   - 开口照叠加在微笑照之上，支持调节开口照的透明度，以便能看到下方的微笑照，实现牙齿设计效果的预览。
2. 牙齿曲线库与数据结构
   - 设计并导入6套牙齿曲线模板（Triangle、Oval、Square、TriangleM、OvalM、SquareM），每套5颗牙齿（左右对称，渲染时自动镜像为10颗）。
   - 每颗牙齿用点集/贝塞尔曲线描述，支持后续精细编辑。
   - 曲线库可切换，切换后自动刷新贴面形状。
3. 贴面整体框架设计与控制
   - 实现贴面整体框架，支持：
     - 拖动整体移动
     - 整体缩放（宽高比可变）
     - 上下拉伸（整体高度调整）
     - 四角垂直拉伸（分别调整上下两条框线的形状，影响牙齿排布的弧度）
   - 框架变换实时影响所有牙齿贴片的位置和形状。
4. 单颗牙齿的精细调整
   - 支持单颗牙齿的平移、旋转、缩放。
   - 支持拖动牙齿曲线的控制点，微调牙齿外形。
   - 编辑时高亮当前牙齿，支持撤销/重做。
5. 镜像与连锁调整
   - 镜像调整：左侧牙齿调整后，右侧自动同步镜像。
   - 连锁调整：相邻牙齿可选择同步调整（如高度、宽度等）。
6. 辅助功能
   - 贴面纹理显示/隐藏
   - 线框显示/隐藏
   - 前后照片切换与透明度调节
   - 镜像拷贝（单侧调整后可一键复制到对侧）
7. 状态管理与交互
   - 用Pinia管理当前曲线库、框架参数、选中牙齿、编辑状态等。
   - 交互动画流畅，操作反馈及时，支持快捷键撤销/重做。

# Test Strategy:
- 验证6套牙齿曲线库的切换与渲染效果。
- 测试整体框架的拖动、缩放、拉伸等操作对贴面排布的影响。
- 检查单颗牙齿的平移、旋转、缩放、曲线编辑等功能的准确性和易用性。
- 验证镜像、连锁、镜像拷贝等批量调整功能的正确性。
- 检查辅助功能（纹理、线框、前后照片切换等）是否正常。
- 确保所有操作可撤销/重做，交互流畅无卡顿。
- 验证微笑照和开口照的加载、对齐和叠加显示功能。
- 测试开口照透明度调节功能的效果和流畅性。

# Subtasks:
## 7. 照片加载与对齐系统 [done]
### Dependencies: None
### Description: 实现微笑照和开口照的加载、对齐和叠加显示功能，支持透明度调节
### Details:
实现步骤：
1. 照片加载功能
   - 从前置步骤获取微笑照和开口照
   - 实现照片加载和缓存机制
   - 处理加载错误和异常情况
2. 照片对齐功能
   - 利用前置步骤中的对齐参数
   - 实现照片在画布中的对齐、居中和聚焦显示
   - 参考预览组件的实现方式
3. 照片叠加显示
   - 实现开口照叠加在微笑照之上的分层显示
   - 开发图层管理系统
4. 透明度调节功能
   - 实现开口照透明度滑块控制
   - 开发透明度变化的平滑过渡效果

测试方法：
1. 功能测试：验证照片加载、对齐和叠加显示功能
2. 视觉测试：确认透明度调节效果
3. 性能测试：测试大尺寸照片的加载和显示性能
4. 用户体验测试：评估照片切换和透明度调节的流畅性

## 1. 牙齿曲线库数据结构与模板导入 [done]
### Dependencies: 6.7
### Description: 设计并实现牙齿曲线库的数据结构，导入6套牙齿曲线模板，并构建模板切换机制
### Details:
实现步骤：
1. 设计牙齿曲线数据结构，使用贝塞尔曲线描述每颗牙齿轮廓
   - 定义基础数据结构：`Tooth`（单颗牙齿）、`TeethSet`（牙齿套装）
   - 每颗牙齿包含：ID、位置信息、控制点数组、变换矩阵等
2. 导入6套预定义牙齿曲线模板：Triangle、Oval、Square、TriangleM、OvalM、SquareM
   - 每套模板包含5颗牙齿（中切牙到第二前磨牙）
   - 设计JSON格式存储模板数据
3. 实现模板管理系统
   - 使用Pinia创建模板状态管理store
   - 实现模板加载、切换和缓存功能
4. 添加模板预览功能
   - 实现模板缩略图生成
   - 开发模板选择器UI组件

测试方法：
1. 单元测试：验证数据结构完整性和模板加载功能
2. 视觉测试：确认不同模板加载后的渲染效果
3. 交互测试：验证模板切换时UI正确更新

## 2. 贴面渲染与框架控制系统 [done]
### Dependencies: 6.1
### Description: 实现贴面的基础渲染功能和整体框架控制系统，包括移动、缩放和形状调整
### Details:
实现步骤：
1. 开发贴面渲染引擎
   - 基于Canvas/WebGL实现牙齿曲线的渲染
   - 实现左右镜像渲染（5颗牙齿镜像为10颗）
   - 添加贴面纹理和材质效果
2. 设计整体框架控制系统
   - 实现框架四边形的绘制和控制点
   - 添加框架操作句柄（控制点）
3. 实现框架交互功能
   - 整体拖动移动功能
   - 整体等比/非等比缩放功能
   - 上下拉伸调整高度
   - 四角垂直拉伸调整弧度
4. 框架变换与牙齿联动
   - 实现框架变换时牙齿位置和形状的实时更新
   - 开发变换矩阵计算工具函数

测试方法：
1. 功能测试：验证所有框架控制功能是否正常工作
2. 性能测试：确保渲染和交互的流畅性
3. 视觉测试：验证框架调整时牙齿变化的视觉效果
4. 边界测试：测试极限缩放和变形情况

## 3. 单颗牙齿精细调整功能 [done]
### Dependencies: 6.1, 6.2
### Description: 实现单颗牙齿的选择与精细调整功能，包括平移、旋转、缩放和形状微调
### Details:
实现步骤：
1. 开发牙齿选择系统
   - 实现点击选择单颗牙齿功能
   - 添加选中状态高亮显示
   - 设计选择工具栏UI
2. 实现单颗牙齿变换功能
   - 添加平移控制（拖拽移动）
   - 实现旋转控制（旋转手柄）
   - 开发缩放控制（缩放手柄）
3. 开发牙齿形状微调功能
   - 显示牙齿曲线控制点
   - 实现控制点拖拽调整曲线形状
   - 添加曲线平滑处理
4. 集成撤销/重做功能
   - 实现操作历史记录
   - 添加撤销/重做快捷键支持
   - 开发操作状态管理系统

测试方法：
1. 交互测试：验证选择和调整的交互流畅性
2. 功能测试：测试所有变换功能的准确性
3. 视觉测试：确认调整后的视觉效果
4. 用户体验测试：评估整体操作的直观性和便捷性

## 4. 镜像调整与同步系统 [done]
### Dependencies: 6.3
### Description: 实现牙齿左右镜像调整功能，保证对称性，并开发镜像拷贝功能
### Details:
实现步骤：
1. 设计镜像关系数据结构
   - 定义牙齿对称映射关系
   - 实现镜像变换计算函数
2. 开发实时镜像同步功能
   - 监听单侧牙齿变化事件
   - 实现对称位置牙齿的实时更新
   - 处理特殊情况（如中线牙齿）
3. 实现镜像模式切换
   - 添加镜像模式开关
   - 支持独立编辑模式与镜像编辑模式切换
4. 开发镜像拷贝功能
   - 实现单侧到对侧的一键复制
   - 添加镜像拷贝方向选择
   - 开发拷贝动画效果

测试方法：
1. 功能测试：验证镜像同步的准确性
2. 边界测试：测试特殊情况下的镜像行为
3. 用户体验测试：评估镜像操作的直观性
4. 视觉测试：确认镜像效果的对称性

## 5. 连锁调整系统 [done]
### Dependencies: 6.3
### Description: 实现相邻牙齿的连锁调整功能，使多颗牙齿能同步变化特定属性
### Details:
实现步骤：
1. 设计连锁调整数据结构
   - 定义连锁组和连锁属性
   - 实现牙齿关系图（邻接矩阵/列表）
2. 开发连锁调整UI
   - 添加连锁模式选择器
   - 实现连锁范围可视化
   - 设计连锁属性选择面板
3. 实现连锁调整逻辑
   - 支持高度连锁调整
   - 支持宽度连锁调整
   - 支持角度连锁调整
   - 支持间距连锁调整
4. 添加高级连锁功能
   - 实现渐变连锁（属性逐渐变化）
   - 开发连锁组保存和加载
   - 添加连锁预设模板

测试方法：
1. 功能测试：验证各种连锁调整的准确性
2. 用户体验测试：评估连锁操作的直观性和效率
3. 边界测试：测试复杂连锁场景
4. 性能测试：确保多牙齿连锁调整的流畅性

## 6. 辅助功能与状态管理整合 [done]
### Dependencies: 6.1, 6.2, 6.3, 6.4, 6.5
### Description: 实现各种辅助功能，并整合状态管理系统，提升整体用户体验
### Details:
实现步骤：
1. 开发视图控制功能
   - 实现贴面纹理显示/隐藏切换
   - 添加线框显示/隐藏功能
   - 开发前后照片切换功能
   - 实现照片透明度调节
2. 整合Pinia状态管理
   - 设计完整的状态管理结构
   - 整合曲线库、框架参数、选中状态等
   - 实现状态持久化（本地存储）
3. 优化交互体验
   - 添加操作反馈动画
   - 实现快捷键系统
   - 优化拖拽和调整的流畅度
4. 开发导出与共享功能
   - 实现设计方案保存
   - 添加图片导出功能
   - 支持设计参数导出

测试方法：
1. 集成测试：验证所有功能模块的协同工作
2. 用户体验测试：评估整体操作流程和体验
3. 性能测试：确保在各种设备上的流畅运行
4. 兼容性测试：测试在不同浏览器中的表现

