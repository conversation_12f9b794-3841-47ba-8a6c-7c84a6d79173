# Task ID: 11
# Title: 实现顶部步骤区域（StepBar）
# Status: done
# Dependencies: 1
# Priority: high
# Description: 开发一个顶部步骤条（StepBar）组件，用于引导用户完成从照片导入到最终预览和保存的8步微笑设计工作流程。
# Details:
创建一个响应式的StepBar组件，显示完整的工作流程，具有以下要求：

1. 包含8个不同的步骤，每个步骤都有相应的图标和文本标签：
   - 导入照片
   - 定位关键点
   - 唇线编辑
   - 照片对齐
   - 设计微笑
   - 选择纹理
   - 精细调整
   - 结果预览与保存

2. 为每个步骤实现三种视觉状态：
   - 已完成：视觉上指示用户已完成的步骤
   - 当前：突出显示用户正在进行的活动步骤
   - 即将进行：以中性状态显示未来步骤

3. 创建一个组件API，允许：
   - 以编程方式设置当前活动步骤
   - 更新步骤状态（已完成/当前/即将进行）
   - 处理用户点击以在步骤之间导航（如果允许）
   - 禁用在满足先决条件之前无法访问的步骤

4. 与应用程序的状态管理（如Pinia）集成，以：
   - 与实际工作流程进度同步
   - 当用户在不同功能模块之间导航时更新
   - 在会话之间保持步骤完成状态

5. 确保组件在不同屏幕尺寸上都可访问且响应式。

6. 参考提供的设计文件（@00-1至@00-8）进行视觉样式、图标和布局要求的实现。

# Test Strategy:
1. 单元测试：
   - 验证StepBar渲染所有8个步骤，并带有正确的图标和标签
   - 测试步骤状态（已完成/当前/即将进行）是否以适当的视觉样式渲染
   - 确认组件API正确运行，用于设置活动步骤和更新状态

2. 集成测试：
   - 验证在不同应用程序模块之间导航时StepBar正确更新
   - 测试点击已完成的步骤是否导航到相应的模块
   - 确保在完成先决条件之前无法访问禁用的步骤

3. 视觉测试：
   - 将渲染的组件与参考文件中的设计规范进行比较
   - 验证在不同屏幕尺寸上的响应式行为
   - 检查悬停/焦点状态是否按预期工作

4. 用户流程测试：
   - 完成端到端工作流程，并验证StepBar是否准确反映进度
   - 测试边缘情况，如跳过步骤或返回到之前的步骤
   - 验证离开并返回应用程序时步骤状态的持久性

5. 可访问性测试：
   - 确保组件可通过键盘导航
   - 验证屏幕阅读器兼容性
   - 检查颜色对比度是否符合可访问性标准

# Subtasks:
## 1. UI结构与样式实现 [done]
### Dependencies: None
### Description: 参考@00-1至@00-8设计图，实现顶部步骤条的基础布局、图标、文字、响应式样式。
### Details:


## 2. 步骤状态与高亮逻辑 [done]
### Dependencies: None
### Description: 实现步骤的“已完成/当前/未完成”三种状态切换及高亮效果。
### Details:


## 3. 组件API与交互 [done]
### Dependencies: None
### Description: 设计并实现组件的API（如设置当前步骤、步骤跳转、禁用未解锁步骤等），并处理用户点击交互。
### Details:


## 4. 与全局状态管理集成 [done]
### Dependencies: None
### Description: 将步骤进度与Pinia等全局状态管理联动，实现进度同步和持久化。
### Details:


## 5. 与业务模块联动 [done]
### Dependencies: None
### Description: 步骤栏与各业务模块（如图片导入、唇线编辑等）联动，确保切换步骤时页面内容同步切换。
### Details:


## 6. 可访问性与多端适配 [done]
### Dependencies: None
### Description: 保证键盘可操作、屏幕阅读器兼容、色彩对比达标，并适配不同屏幕尺寸。
### Details:


## 7. 单元测试与集成测试 [done]
### Dependencies: None
### Description: 针对上述功能点编写单元测试和集成测试用例，确保组件稳定性。
### Details:


