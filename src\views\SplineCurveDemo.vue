<template>
  <div class="spline-curve-demo">
    <h2>样条曲线编辑器演示</h2>

    <div class="editor-container">
      <v-stage :config="stageConfig">
        <v-layer>
          <SplineCurveEditor
            v-model:points="points"
            :editable="editable"
            v-model:closed="closed"
            :tension="tension"
            :curveColor="curveColor"
            :curveWidth="curveWidth"
            :pointRadius="pointRadius"
            :pointColor="pointColor"
            :firstPointColor="firstPointColor"
            :allowAddPointsWhenClosed="allowAddPointsWhenClosed"
            @change="handleChange"
            @close="handleClose"
            @open="handleOpen"
          />
        </v-layer>
      </v-stage>
    </div>

    <div class="controls">
      <h2>控制面板</h2>
      <!-- ...原有控制面板内容不变... -->
      <div class="control-group">
        <label>张力系数:</label>
        <input type="range" v-model.number="tension" min="0" max="1" step="0.1" />
        <span>{{ tension }}</span>
      </div>
      <div class="control-group">
        <label>曲线颜色:</label>
        <input type="color" v-model="curveColor" />
      </div>
      <div class="control-group">
        <label>曲线宽度:</label>
        <input type="range" v-model.number="curveWidth" min="1" max="10" step="1" />
        <span>{{ curveWidth }}px</span>
      </div>
      <div class="control-group">
        <label>控制点半径:</label>
        <input type="range" v-model.number="pointRadius" min="3" max="15" step="1" />
        <span>{{ pointRadius }}px</span>
      </div>
      <div class="control-group">
        <label>控制点颜色:</label>
        <input type="color" v-model="pointColor" />
      </div>
      <div class="control-group">
        <label>第一个点颜色:</label>
        <input type="color" v-model="firstPointColor" />
      </div>
      <div class="control-group">
        <label>闭合后允许添加点:</label>
        <input type="checkbox" v-model="allowAddPointsWhenClosed" />
      </div>
      <div class="control-group">
        <label>闭合状态:</label>
        <input type="checkbox" v-model="closed" />
      </div>
      <div class="control-group">
        <label>可编辑:</label>
        <input type="checkbox" v-model="editable" />
      </div>
      <div class="control-group">
        <button @click="resetPoints">重置点</button>
        <button @click="clearPoints">清空点</button>
      </div>
    </div>

    <div class="data-display">
      <h2>数据</h2>
      <pre>{{ JSON.stringify(points, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import SplineCurveEditor from '@/components/lip/SplineCurveEditor.vue'

// 初始点数据
const initialPoints = [
  { x: 100, y: 100 },
  { x: 200, y: 50 },
  { x: 300, y: 150 },
  { x: 400, y: 100 }
]

// 响应式状态
const points = ref([...initialPoints])
const closed = ref(false)
const tension = ref(0.3)
const curveColor = ref('#2080ff')
const curveWidth = ref(3)
const pointRadius = ref(8)
const pointColor = ref('#6b9aff')
const firstPointColor = ref('#ff6b6b')
const allowAddPointsWhenClosed = ref(true)
const editable = ref(true)

// 舞台配置
const stageConfig = {
  width: 800,
  height: 600
}

// 事件处理
const handleChange = (newPoints: { x: number; y: number }[]) => {
  console.log('Points changed:', newPoints)
  // points.value = newPoints
  // console.log(points.value)
}

watch(points, (newPoints) => {
  // console.log('Points changed:', newPoints)
})

const handleClose = () => {
  console.log('Curve closed')
}

const handleOpen = () => {
  console.log('Curve opened')
}

// 重置点
const resetPoints = () => {
  points.value = [...initialPoints]
  closed.value = false
}

// 清空点
const clearPoints = () => {
  points.value = []
  closed.value = false
}
</script>

<!-- 样式不变 -->
