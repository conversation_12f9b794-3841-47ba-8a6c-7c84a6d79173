<template>
  <div
    class="canvas-container"
    @wheel.prevent="editorContext.handleWheel"
    @mousedown="editorContext.handleMouseDown"
    @mousemove="editorContext.handleMouseMove"
    @mouseup="editorContext.handleMouseUp"
    @mouseleave="editorContext.handleMouseUp"
    @contextmenu.prevent
    ref="canvasContainerRef"
  >
    <v-stage
      v-if="editorContext.smileImgObj"
      :config="{ width: editorContext.stageWidth.value, height: editorContext.stageHeight.value }"
      @click="handleStageClick"
    >
      <!-- 自定义底图层，始终在最底层 -->
      <v-layer :key="'custom-bg'">
        <v-image
          v-show="props.customBgImgObj"
          :image="props.customBgImgObj"
          :x="props.customBgTransform?.x ?? 0"
          :y="props.customBgTransform?.y ?? 0"
          :scaleX="props.customBgTransform?.scale ?? 1"
          :scaleY="props.customBgTransform?.scale ?? 1"
          :rotation="props.customBgTransform?.rotation ?? 0"
          :draggable="true"
          @dragmove="handleCustomBgDragMove"
          @wheel="handleCustomBgWheel"
          :offsetX="props.customBgImgObj?.width ? props.customBgImgObj.width / 2 : 0"
          :offsetY="props.customBgImgObj?.height ? props.customBgImgObj.height / 2 : 0"
        />
      </v-layer>
      <!-- 恢复主图层，并用 hideSmileImage 控制显示 -->
      <v-layer ref="mainLayerRef" v-if="!props.hideSmileImage">
        <v-image
          ref="smileImgRef"
          :image="editorContext.smileImgObj.value"
          :x="editorContext.imgCenter.value.x"
          :y="editorContext.imgCenter.value.y"
          :offsetX="editorContext.smileImgNaturalWidth.value / 2"
          :offsetY="editorContext.smileImgNaturalHeight.value / 2"
          :scaleX="editorContext.imgScale.value"
          :scaleY="editorContext.imgScale.value"
          :rotation="(editorContext.faceRotation.value * 180) / Math.PI"
          :listening="false"
          :perfectDrawEnabled="false"
        />
        <v-image
          v-if="!isTexturePage"
          v-show="showMouthImage && editorContext.mouthRenderParams.value"
          ref="mouthImgRef"
          :image="editorContext.mouthImgObj.value"
          :x="mouthX"
          :y="mouthY"
          :offsetX="editorContext.mouthImgNaturalWidth.value / 2"
          :offsetY="editorContext.mouthImgNaturalHeight.value / 2"
          :scaleX="mouthScale"
          :scaleY="mouthScale"
          :rotation="mouthRotation"
          :opacity="editorContext.mouthOpacity.value"
          :listening="false"
          :perfectDrawEnabled="false"
        />
      </v-layer>
      <!-- 中线 -->
      <v-layer ref="midlineLayerRef">
        <v-line
          v-if="showMidline"
          :points="[midlineStageX, 0, midlineStageX, editorContext.stageHeight.value]"
          stroke="#ff6b6b"
          :strokeWidth="2"
          :dash="[8, 6]"
          :listening="false"
        />
      </v-layer>

      <!-- 牙齿渲染层 - 根据isTexturePage属性决定使用哪个渲染器 -->
      <v-layer ref="teethLayerRef">
        <!-- 贴面设计页面使用交互式牙齿渲染器 -->
        <TeethRenderer
          v-if="!isTexturePage && frameReady"
          ref="teethRendererRef"
          :stageScale="editorContext.imgScale.value"
          :showSmileFrame="showSmileFrame"
          :showControls="showFrameControls"
          :interactive="interactiveTeeth"
        />

        <!-- 纹理页面使用专用的纹理牙齿渲染器 -->
        <TextureTeethRenderer
          v-else-if="isTexturePage && frameReady"
          ref="textureTeethRendererRef"
          :stageScale="editorContext.imgScale.value"
          :showTexture="showTexture"
          :showSmileFrame="showSmileFrame"
        />
      </v-layer>
    </v-stage>
  </div>
</template>

<script setup lang="ts">
import { shallowRef, computed, onMounted, watch, nextTick, inject } from 'vue'
import { useCommonStore } from '@/store/common'
import { useTeethStore } from '@/store/teeth'
import { useTextureStore } from '@/store/texture'
import { storeToRefs } from 'pinia'
import { calcMidlineXOnStage } from '@/utils/faceMath'
import { imageToStage as imageToStageUtil, getLipBoundingBox } from '@/utils/transform2d'
import { TARGET_RATIO } from '@/composables/veneer/useCanvasStage'
import { useVeneerEditorContext } from '@/contexts/VeneerEditorContext'
import { TextureEditorKey } from '@/contexts/TextureEditorContext'
import TeethRenderer from '@/components/veneer/TeethRenderer.vue'
import TextureTeethRenderer from '@/components/texture/TextureTeethRenderer.vue'

// Props
const props = defineProps({
  showMidline: {
    type: Boolean,
    default: true
  },
  showSmileFrame: {
    type: Boolean,
    default: true
  },
  showMouthImage: {
    type: Boolean,
    default: true
  },
  showFrameControls: {
    type: Boolean,
    default: true
  },
  interactiveTeeth: {
    type: Boolean,
    default: true
  },
  isTexturePage: {
    type: Boolean,
    default: false
  },
  customBgImgObj: {
    type: Object as () => HTMLImageElement | null,
    default: null
  },
  customBgTransform: {
    type: Object as () => {
      x: number
      y: number
      scale: number
      rotation: number
    } | null,
    default: null
  },
  hideSmileImage: {
    type: Boolean,
    default: false
  }
})

// 根据isTexturePage决定使用哪个编辑器上下文
let editorContext: any
if (props.isTexturePage) {
  // 在纹理页面中，使用TextureEditorContext
  editorContext = inject(TextureEditorKey)
  if (!editorContext) {
    throw new Error('TextureEditorContext not provided')
  }
} else {
  // 在贴面设计页面中，使用VeneerEditorContext
  editorContext = useVeneerEditorContext()
}

// 从store获取数据
const commonStore = useCommonStore()
const teethStore = useTeethStore()
const textureStore = useTextureStore()
const { showTexture } = storeToRefs(textureStore)

// 初始化牙齿框架位置和缓存图像
onMounted(() => {
  // 当组件挂载后，根据画布尺寸和唇线位置初始化牙齿框架
  nextTick(() => {
    // 只在第一次加载时设置框架位置
    // 检查框架是否已经有位置信息
    const currentFrame = teethStore.frame
    const needsInitialPosition = currentFrame.x === 0 && currentFrame.y === 0

    if (needsInitialPosition) {
      initFrameBasedOnLipPosition()
    }

    // 缓存微笑照片
    if (smileImgRef.value) {
      const smileNode = smileImgRef.value.getNode()
      if (smileNode) {
        console.log('缓存微笑照片')
        smileNode.cache()
      }
    }

    // 不再缓存开口照片，因为它的透明度会频繁变化
    // 对于频繁变化的元素，缓存可能会降低性能而不是提高性能
  })
})

// 基于唇线位置初始化框架
function initFrameBasedOnLipPosition() {
  // 重置框架位置前，先重置自定义牙齿形状
  const lipPoints = commonStore.smileLipPoints

  // 延迟执行，确保图片已经重新聚焦
  nextTick(() => {
    if (lipPoints && lipPoints.length > 0) {
      // 使用唇线点计算框架位置和大小
      // 1. 将唇线点从图片坐标转换到画布坐标
      const lipPointsInStage = lipPoints.map((point) =>
        imageToStageUtil(point, editorContext.getTransformParams())
      )

      // 2. 使用getLipBoundingBox计算唇线的包围盒
      const box = getLipBoundingBox(lipPointsInStage)
      const minX = box.minX
      const maxX = box.maxX
      const minY = box.minY
      const maxY = box.maxY

      // 3. 基于唇线包围盒设置框架位置和大小
      // 框架宽度略大于唇线宽度，高度适当调整
      const lipWidth = maxX - minX
      const lipHeight = maxY - minY
      const lipCenterX = (minX + maxX) / 2
      const lipCenterY = (minY + maxY) / 2

      // 使用全局配置常量，框架宽度与TARGET_RATIO保持一致
      const frameWidth = lipWidth * (TARGET_RATIO / 0.5)
      const frameHeight = frameWidth * 0.08 // 框架高度，通过宽度的比例来计算（它代表两条曲线的两端点垂直距离）

      // 框架中心相对唇线中心来定位
      const frameX = lipCenterX - frameWidth / 2
      const frameY = lipCenterY - frameHeight / 2 - lipHeight * 0.6 // 往上移，因为唇线是包括上下牙齿的，框架只做上排牙齿

      // 更新框架位置和大小，并初始化曲线控制点
      teethStore.updateFrame({
        x: frameX,
        y: frameY,
        width: frameWidth,
        height: frameHeight,
        // 初始化曲线控制点，保持与默认模板相同的比例
        topCurvePoints: {
          leftY: 0,
          midY: frameHeight * 0.4, // 下沉
          rightY: 0
        },
        bottomCurvePoints: {
          leftY: frameHeight,
          midY: frameHeight * 2.4, // 下沉
          rightY: frameHeight
        }
      })

      console.log('框架已基于唇线位置初始化')
    } else {
      // 如果没有唇线数据，则使用默认的中央位置
      console.log('没有唇线数据，使用默认中央位置')

      // 使用全局配置常量
      const frameWidth = Math.min(700, editorContext.stageWidth.value * TARGET_RATIO)
      const frameHeight = frameWidth * 0.25 // 减小框架高度比例，从0.3减小到0.25
      const frameX = (editorContext.stageWidth.value - frameWidth) / 2
      const frameY = editorContext.stageHeight.value / 2 - frameHeight / 2

      teethStore.updateFrame({
        x: frameX,
        y: frameY,
        width: frameWidth,
        height: frameHeight,
        // 初始化曲线控制点，保持与默认模板相同的比例
        topCurvePoints: {
          leftY: 0,
          midY: frameHeight * 0.1, // 初始下沉为框架高度的10%
          rightY: 0
        },
        bottomCurvePoints: {
          leftY: frameHeight,
          midY: frameHeight * 1.2, // 减小下沉比例，从1.27减小到1.2，即下沉20%
          rightY: frameHeight
        }
      })
    }
  })
}

// 中线在画布坐标系下的 x 坐标
const midlineStageX = computed(() => {
  // 获取 store 中的面部关键点
  const smileFaceLandmarks = commonStore.smileFaceLandmarks

  // 定义图片坐标到画布坐标的转换函数
  const imageToStage = (pt: { x: number; y: number }) => {
    return imageToStageUtil(pt, editorContext.getTransformParams())
  }

  // 使用新方法：先将各点转换到画布坐标系，再计算中点
  return calcMidlineXOnStage(smileFaceLandmarks, imageToStage)
})

// 优化开口照渲染参数，避免在每次渲染时重新计算
const mouthX = computed(() => {
  if (props.isTexturePage) return 0
  return editorContext.mouthRenderParams?.value ? editorContext.mouthRenderParams.value.x : 0
})

const mouthY = computed(() => {
  if (props.isTexturePage) return 0
  return editorContext.mouthRenderParams?.value ? editorContext.mouthRenderParams.value.y : 0
})

const mouthScale = computed(() => {
  if (props.isTexturePage) return 1
  return editorContext.mouthRenderParams?.value ? editorContext.mouthRenderParams.value.scale : 1
})

const mouthRotation = computed(() => {
  if (props.isTexturePage) return 0
  return editorContext.mouthRenderParams?.value
    ? (editorContext.mouthRenderParams.value.angle * 180) / Math.PI
    : 0
})

// 监听图片缩放和移动，同步更新框架位置和大小
watch(
  () => [
    editorContext.imgScale.value,
    editorContext.imgCenter.value.x,
    editorContext.imgCenter.value.y
  ],
  (newValues, oldValues) => {
    // 类型安全处理
    const newScale = newValues[0] as number
    const newCenterX = newValues[1] as number
    const newCenterY = newValues[2] as number
    const oldScale = oldValues?.[0] as number | undefined
    const oldCenterX = oldValues?.[1] as number | undefined
    const oldCenterY = oldValues?.[2] as number | undefined

    if (!oldScale || oldCenterX === undefined || oldCenterY === undefined) return // 首次加载不处理

    // 如果是纹理页面，使用TextureEditorContext中的updateLocalFramePosition方法
    if (props.isTexturePage) {
      // 纹理页面中的框架位置更新由TextureEditorContext处理
      // 这里只更新局部框架位置，不影响全局TeethStore
      editorContext.updateLocalFramePosition()
      return
    }

    // 以下是贴面设计页面的框架位置更新逻辑
    // 获取当前框架
    const currentFrame = teethStore.frame

    // 计算缩放比例
    const scaleRatio = newScale / oldScale

    // 计算框架中心点
    const frameCenterX = currentFrame.x + currentFrame.width / 2
    const frameCenterY = currentFrame.y + currentFrame.height / 2

    // 计算框架中心点相对于图片中心点的偏移量
    const deltaFromCenterX = frameCenterX - oldCenterX
    const deltaFromCenterY = frameCenterY - oldCenterY

    // 应用缩放比例到偏移量
    const newDeltaFromCenterX = deltaFromCenterX * scaleRatio
    const newDeltaFromCenterY = deltaFromCenterY * scaleRatio

    // 计算新的框架中心点
    const newFrameCenterX = newCenterX + newDeltaFromCenterX
    const newFrameCenterY = newCenterY + newDeltaFromCenterY

    // 计算新的框架尺寸
    const newFrameWidth = currentFrame.width * scaleRatio
    const newFrameHeight = currentFrame.height * scaleRatio

    // 计算新的框架左上角坐标
    const newFrameX = newFrameCenterX - newFrameWidth / 2
    const newFrameY = newFrameCenterY - newFrameHeight / 2

    // 获取当前曲线控制点
    const { topCurvePoints, bottomCurvePoints } = currentFrame

    // 计算新的曲线控制点位置
    // 注意：曲线控制点的Y坐标是相对于框架的Y坐标的，所以我们需要保持它们相对于框架高度的比例
    const newTopCurvePoints = topCurvePoints
      ? {
          leftY: topCurvePoints.leftY * scaleRatio, // 保持相对于框架高度的比例
          midY: topCurvePoints.midY * scaleRatio,
          rightY: topCurvePoints.rightY * scaleRatio
        }
      : undefined

    const newBottomCurvePoints = bottomCurvePoints
      ? {
          leftY: bottomCurvePoints.leftY * scaleRatio, // 保持相对于框架高度的比例
          midY: bottomCurvePoints.midY * scaleRatio,
          rightY: bottomCurvePoints.rightY * scaleRatio
        }
      : undefined

    // 更新框架，包括曲线控制点
    teethStore.updateFrame({
      x: newFrameX,
      y: newFrameY,
      width: newFrameWidth,
      height: newFrameHeight,
      topCurvePoints: newTopCurvePoints,
      bottomCurvePoints: newBottomCurvePoints
    })
  }
)

// 容器引用 - 使用shallowRef减少不必要的深度响应式监听
const canvasContainerRef = shallowRef<HTMLElement | null>(null)
const mainLayerRef = shallowRef<any>(null)
const midlineLayerRef = shallowRef<any>(null)
const teethLayerRef = shallowRef<any>(null)
const smileImgRef = shallowRef<any>(null)
const mouthImgRef = shallowRef<any>(null)
const teethRendererRef = shallowRef<any>(null)
const textureTeethRendererRef = shallowRef<any>(null)

// 处理舞台点击事件
function handleStageClick(e: any) {
  // 检查点击的目标元素
  const target = e.target
  const className = target.className || ''

  // 如果点击的不是牙齿相关元素（Line, Rect等），且当前有选中的牙齿，则取消选中
  // 牙齿相关元素通常是这些类型
  const isToothElement = ['Line', 'Rect'].includes(className)

  if (!isToothElement && teethStore.selectedToothId !== null) {
    teethStore.selectTooth(null)
  }
}

// 暴露方法给父组件
defineExpose({
  canvasContainerRef,
  initFrameBasedOnLipPosition, // 暴露初始化框架方法给父组件
  mainLayerRef,
  midlineLayerRef,
  teethLayerRef,
  teethRendererRef,
  textureTeethRendererRef
})

const frameReady = computed(() => {
  const f = teethStore.frame
  // 只要不是初始值就算准备好了
  return !(f.x === 0 && f.y === 0)
})

const emits = defineEmits(['update:customBgTransform'])

function handleCustomBgDragMove(e: any) {
  if (!props.customBgTransform) return
  const node = e.target
  emits('update:customBgTransform', {
    ...props.customBgTransform,
    x: node.x(),
    y: node.y()
  })
}

function handleCustomBgWheel(e: any) {
  if (!props.customBgTransform) return
  e.evt.preventDefault()
  const scale = props.customBgTransform.scale ?? 1
  const delta = e.evt.deltaY > 0 ? -0.05 : 0.05
  const newScale = Math.max(0.1, scale + delta)
  emits('update:customBgTransform', {
    ...props.customBgTransform,
    scale: newScale
  })
}
</script>

<style scoped>
.canvas-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  z-index: 1;
}
</style>
