import { ref, computed } from 'vue'
import { useFaceData } from './useFaceData'
import type { TransformParams } from '@/utils/transform2d'
import {
  imageToStage as imageToStageUtil,
  stageToImage as stageToImageUtil
} from '@/utils/transform2d'

/**
 * 唇线编辑相关功能的组合式函数
 * 提供唇线编辑、重置和坐标转换功能
 *
 * @param getTransformParams 获取图像变换参数的函数
 * @returns 唇线编辑相关的状态和方法
 */
export function useLipEdit(getTransformParams: () => TransformParams) {
  // 使用面部数据管理
  const faceData = useFaceData()

  // 本地状态
  const closed = ref(true) // 唇线是否闭合
  const editable = ref(false) // 是否可编辑

  /**
   * 重置唇线点
   */
  function resetLipPoints() {
    faceData.resetLipPoints()
  }

  /**
   * 图片坐标转换为画布坐标
   * @param point 图片坐标点
   * @returns 画布坐标点
   */
  function imageToStage(point: { x: number; y: number }) {
    return imageToStageUtil(point, getTransformParams())
  }

  /**
   * 画布坐标转换为图片坐标
   * @param point 画布坐标点
   * @returns 图片坐标点
   */
  function stageToImage(point: { x: number; y: number }) {
    return stageToImageUtil(point, getTransformParams())
  }

  /**
   * 计算属性：实现 lipPoints <-> 画布坐标的双向绑定
   * get：将原始图片坐标 lipPoints 映射为画布坐标，供 SplineCurveEditor 渲染
   * set：将 SplineCurveEditor 编辑后的画布坐标逆变换为原始图片坐标，回写 lipPoints
   */
  const transformedLipPoints = computed({
    // 计算画布坐标（组件获取值，内部使用）
    get() {
      if (!faceData.smileLipPoints.value) return []
      return faceData.smileLipPoints.value.map((p: { x: number; y: number }) => imageToStage(p))
    },
    // 设置原始图片坐标（组件往外传值）
    set(newPoints) {
      if (!Array.isArray(newPoints)) return
      faceData.smileLipPoints.value = newPoints.map((p: { x: number; y: number }) =>
        stageToImage(p)
      )
    }
  })

  /**
   * 初始化唇线点
   * 注意：AI检测数据已在ImportImage页面获取，这里只是一个空方法保持接口兼容
   */
  async function initLipPoints(_width?: number, _height?: number) {
    // 不再需要重复获取AI检测数据
    console.log('initLipPoints: AI检测数据已在ImportImage页面获取')
  }

  /**
   * 激活编辑模式
   */
  function activateEdit() {
    editable.value = true
  }

  /**
   * 取消编辑模式
   */
  function deactivateEdit() {
    if (closed.value) {
      editable.value = false
    }
  }

  /**
   * 添加唇线点位
   * @param point 图片坐标系下的点位
   * @returns 添加后的点位数量
   */
  function addPoint(point: { x: number; y: number }) {
    if (!faceData.smileLipPoints.value) {
      // 如果没有点，创建一个新数组
      faceData.smileLipPoints.value = [point]
      // 立即激活编辑模式，确保第一个点可见
      activateEdit()
    } else {
      // 如果已有点，添加到现有数组
      faceData.smileLipPoints.value.push(point)

      // 如果添加了第二个点，确保编辑模式激活
      if (faceData.smileLipPoints.value.length === 2) {
        activateEdit()
        // 用户手动添加的点位，曲线默认不闭合
        closed.value = false
      }
    }

    return faceData.smileLipPoints.value.length
  }

  /**
   * 设置曲线闭合状态
   * @param value 是否闭合
   */
  function setClosedState(value: boolean) {
    closed.value = value
  }

  return {
    lipPoints: faceData.smileLipPoints,
    transformedLipPoints,
    closed,
    editable,
    resetLipPoints,
    imageToStage,
    stageToImage,
    initLipPoints,
    activateEdit,
    deactivateEdit,
    addPoint,
    setClosedState
  }
}
