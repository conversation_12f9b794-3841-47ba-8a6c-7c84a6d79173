<template>
  <v-group>
    <!-- 底层v-line：填充基本颜色 -->
    <v-line
      ref="baseLineRef"
      :points="outlinePoints"
      :config="baseLineConfig"
      :listening="false"
      closed="true"
      :tension="0.4"
    />
    <!-- 顶层v-line：应用纹理和光影效果 -->
    <v-line
      ref="textureLineRef"
      :points="outlinePoints"
      :config="textureLineConfig"
      :listening="false"
      closed="true"
      :tension="0.4"
    />
  </v-group>
</template>

<script setup lang="ts">
import { ref, computed, toRef } from 'vue'
import { useTeethStore, type SegmentInfo } from '@/store/teeth'
import { useToothTexture } from '@/composables/texture/useToothTexture'

// Props
const props = defineProps({
  segmentInfo: {
    type: Object as () => SegmentInfo,
    required: true
  },
  toothId: {
    type: Number,
    required: true
  },
  showTexture: {
    type: Boolean,
    default: true
  }
})

// Store
const teethStore = useTeethStore()

// 底层和顶层线条引用
const baseLineRef = ref<any>(null)
const textureLineRef = ref<any>(null)

// 获取牙齿轮廓点
const outlinePoints = computed(() => {
  // 从store中获取当前牙齿形状
  const toothShape = teethStore.getCurrentToothShape(props.toothId)
  if (!toothShape || !toothShape.points || toothShape.points.length === 0) {
    return []
  }

  // 获取区块信息
  const { startX, width, height, topY } = props.segmentInfo
  const centerX = startX + width / 2
  const centerY = topY + height / 2

  // 将相对坐标转换为绝对坐标
  const points = toothShape.points.map((point) => ({
    x: centerX + (point.x * width) / 2,
    y: centerY + (point.y * height) / 2
  }))

  // 将点数组转换为扁平数组用于 Konva 渲染
  const result = points.flatMap((point) => [point.x, point.y])

  return result
})

// 基础配置对象 - 纹理牙齿不需要交互功能
const baseConfig = computed(() => {
  return {
    draggable: false,
    listening: false,
    hitStrokeWidth: 0
  }
})

// 使用纹理相关功能
const { baseLineConfig, textureLineConfig } = useToothTexture(
  baseLineRef,
  textureLineRef,
  toRef(props, 'showTexture'),
  toRef(props, 'toothId'),
  baseConfig,
  outlinePoints
)
</script>
